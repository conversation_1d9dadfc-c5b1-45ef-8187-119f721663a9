package com.llq.vj.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 航班选择功能测试
 */
public class FlightSelectionTest {
    
    private static final String HTML_FILE_PATH = "src/main/java/com/llq/vj/payment/tast.html";
    
    @Test
    public void testFindSpecificValue() {
        // 测试查找特定值 "4,A1_ECO,O"
        String targetValue = "4,A1_ECO,O";
        String foundValue = HtmlParserUtil.parseRadioButtonValue(HTML_FILE_PATH, targetValue);
        
        assertNotNull(foundValue, "应该能找到目标值");
        assertEquals(targetValue, foundValue, "找到的值应该与目标值一致");
        
        System.out.println("✓ 成功找到目标值: " + foundValue);
    }
    
    @Test
    public void testParseFlightOptionInfo() {
        // 测试解析航班选项详细信息
        String targetValue = "4,A1_ECO,O";
        HtmlParserUtil.FlightOptionInfo info = HtmlParserUtil.parseFlightOptionInfo(HTML_FILE_PATH, targetValue);
        
        assertNotNull(info, "应该能解析出航班选项信息");
        assertEquals(targetValue, info.getValue(), "值应该匹配");
        assertNotNull(info.getFare(), "应该有票价信息");
        
        System.out.println("✓ 航班选项信息:");
        System.out.println("  - 值: " + info.getValue());
        System.out.println("  - 名称: " + info.getName());
        System.out.println("  - 票价: " + info.getFare());
        System.out.println("  - 总票价: " + info.getTotalFare());
        System.out.println("  - 总费用: " + info.getTotalCompleteCharges());
    }
    
    @Test
    public void testSelectA1FareCode() {
        // 测试选择A1票价代码的航班
        FlightSelectionHelper.SelectionCriteria criteria = 
            FlightSelectionHelper.SelectionCriteria.forFareCode("A1");
        criteria.setCabinClass("ECO");
        
        String selectedParam = FlightSelectionHelper.selectBestFlight(HTML_FILE_PATH, criteria);
        
        assertNotNull(selectedParam, "应该能选择到A1票价代码的航班");
        assertTrue(selectedParam.contains("A1_ECO"), "选择的参数应该包含A1_ECO");
        
        System.out.println("✓ 选择的A1票价代码航班: " + selectedParam);
        
        // 验证这就是我们要找的 "4,A1_ECO,O"
        if ("4,A1_ECO,O".equals(selectedParam)) {
            System.out.println("✓ 成功选择到目标参数: 4,A1_ECO,O");
        }
    }
    
    @Test
    public void testGetAllFlightOptions() {
        // 测试获取所有航班选项
        var flightOptions = HtmlParserUtil.parseFlightOptionValues(HTML_FILE_PATH);
        
        assertFalse(flightOptions.isEmpty(), "应该有航班选项");
        assertTrue(flightOptions.contains("4,A1_ECO,O"), "应该包含目标值");
        
        System.out.println("✓ 找到 " + flightOptions.size() + " 个航班选项");
        System.out.println("✓ 包含目标值 4,A1_ECO,O: " + flightOptions.contains("4,A1_ECO,O"));
        
        // 显示所有包含A1的选项
        System.out.println("所有A1票价代码的选项:");
        flightOptions.stream()
                .filter(option -> option.contains("A1_ECO"))
                .forEach(option -> System.out.println("  - " + option));
    }
}
