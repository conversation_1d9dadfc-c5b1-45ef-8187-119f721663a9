package com.llq.vj.util;

import org.junit.jupiter.api.Test;
import java.util.List;

/**
 * HTML解析工具类测试
 * 
 * <AUTHOR>
 * @version 2025/10/27
 */
public class HtmlParserUtilTest {

    @Test
    public void testParseSpecificValue() {
        String htmlFilePath = "src/main/java/com/llq/vj/payment/tast.html";
        String targetValue = "4,A1_ECO,O";
        
        // 查找特定值
        String foundValue = HtmlParserUtil.parseRadioButtonValue(htmlFilePath, targetValue);
        System.out.println("=== 查找特定值 ===");
        System.out.println("目标值: " + targetValue);
        System.out.println("找到的值: " + foundValue);
        System.out.println("是否找到: " + (foundValue != null));
    }

    @Test
    public void testParseAllFlightOptions() {
        String htmlFilePath = "src/main/java/com/llq/vj/payment/tast.html";
        
        // 获取所有航班选项值
        List<String> flightOptions = HtmlParserUtil.parseFlightOptionValues(htmlFilePath);
        System.out.println("=== 所有航班选项值 ===");
        System.out.println("总数: " + flightOptions.size());
        flightOptions.forEach(value -> System.out.println("- " + value));
    }

    @Test
    public void testParseFlightOptionInfo() {
        String htmlFilePath = "src/main/java/com/llq/vj/payment/tast.html";
        String targetValue = "4,A1_ECO,O";
        
        // 获取详细信息
        HtmlParserUtil.FlightOptionInfo info = HtmlParserUtil.parseFlightOptionInfo(htmlFilePath, targetValue);
        System.out.println("=== 航班选项详细信息 ===");
        if (info != null) {
            System.out.println("值: " + info.getValue());
            System.out.println("名称: " + info.getName());
            System.out.println("票价: " + info.getFare());
            System.out.println("总票价: " + info.getTotalFare());
            System.out.println("费用: " + info.getCharges());
            System.out.println("总费用: " + info.getTotalCompleteCharges());
            System.out.println("完整信息: " + info);
        } else {
            System.out.println("未找到该值的详细信息");
        }
    }

    @Test
    public void testParseByRadioName() {
        String htmlFilePath = "src/main/java/com/llq/vj/payment/tast.html";
        
        // 获取出发航班选项
        List<String> depValues = HtmlParserUtil.parseRadioButtonValuesByName(htmlFilePath, "gridTravelOptDep");
        System.out.println("=== 出发航班选项 ===");
        System.out.println("总数: " + depValues.size());
        depValues.forEach(value -> System.out.println("- " + value));
        
        System.out.println();
        
        // 获取返程航班选项
        List<String> retValues = HtmlParserUtil.parseRadioButtonValuesByName(htmlFilePath, "gridTravelOptRet");
        System.out.println("=== 返程航班选项 ===");
        System.out.println("总数: " + retValues.size());
        retValues.forEach(value -> System.out.println("- " + value));
    }

    @Test
    public void testParseAllRadioValues() {
        String htmlFilePath = "src/main/java/com/llq/vj/payment/tast.html";
        
        // 获取所有radio button值
        List<String> allValues = HtmlParserUtil.parseAllRadioButtonValues(htmlFilePath);
        System.out.println("=== 所有Radio Button值 ===");
        System.out.println("总数: " + allValues.size());
        allValues.forEach(value -> System.out.println("- " + value));
    }
}
