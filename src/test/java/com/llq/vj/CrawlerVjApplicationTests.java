package com.llq.vj;

import com.llq.vj.dto.VjResultDataDto;
import com.llq.vj.dto.VjResultDto;
import com.llq.vj.entity.Sys_Log;
import com.llq.vj.service.Lcc_VjGetDataService;
import com.llq.vj.service.Lcc_VjSaveDataService;
import com.llq.vj.service.Sys_LogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@SpringBootTest
@Slf4j
class CrawlerVjApplicationTests {

    @Resource
    private Lcc_VjGetDataService lccS7GetDataService;
    @Resource
    private Lcc_VjSaveDataService lccS7SaveDataService;
    @Resource
    private Sys_LogService logService;

    //@Test
    void testS7Request() throws Exception {
        String tripType = "ONE_WAY";
        String depCity = "PKX"; String arrCity = "OVB";
        String depAirport = "PKX"; String arrAirport = "OVB";
        String departureDate = "2025-08-29";
        String CURRENCY = "RUB";
        VjResultDto dto = lccS7GetDataService.getOneWayVjTicketData(depCity,arrCity,departureDate,CURRENCY,1, depAirport, arrAirport);


        System.out.println("S7-Test-Ret: " + dto.isSuccess());
        System.out.println("S7-Test-Ret-data: " + dto.getData());

        if (dto.isSuccess()){
            //记录日志,保存政策时一批政策用一个日志，不要一个政策生成一个日志
            Sys_Log sysLog=new Sys_Log();
            sysLog.setLogMemberId(141);//以系统中LCC用户的名义发布和更新政策
            sysLog.setLogModuleId(435);//算是在采集政策接口模块中进行的操作
            sysLog.setLogTime(new Date());
            sysLog.setLogTableNames("zx_zhengCe,zx_zhengCe_piao,zx_zhengCe_xingLi,zx_zhengCe_hangDuan,zx_zhengCe_kuCun");//操作可能会对这些表产生影响
            logService.save(sysLog);
            List<VjResultDataDto> data = dto.getData();
            if ( null != data ){
                lccS7SaveDataService.saveOneWay(data,sysLog.getLogId());
            }

        }

    }

   // @Test
    void testS7RequestJson() throws Exception {

       /* StringBuilder content = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new FileReader("D:/json.txt"))) {
            String line;
            while ((line = br.readLine()) != null) {
                content.append(line);
            }
        }
        JSONObject result = JSON.parseObject(content.toString());
        S7DataVo dto =  result.getJSONObject("data").toJavaObject(S7DataVo.class);

        String tripType = "ONE_WAY";
        String depCity = "PKX"; String arrCity = "OVB";
        String departureDate = "2025-08-29";
        String CURRENCY = "RUB";

        VjApiResultVo apiResultVo = new VjApiResultVo();
        apiResultVo.setSuccess(true);
        apiResultVo.setData(dto);
        VjResultDto resultDto = lccS7GetDataService.getOneWayVjTicketData(depCity,arrCity,departureDate,CURRENCY,apiResultVo);


        System.out.println("S7-Test-Ret: " + resultDto.isSuccess());
        System.out.println("S7-Test-Ret-data: " + resultDto.getData());

        if (resultDto.isSuccess()){
            //记录日志,保存政策时一批政策用一个日志，不要一个政策生成一个日志
            Sys_Log sysLog=new Sys_Log();
            sysLog.setLogMemberId(141);//以系统中LCC用户的名义发布和更新政策
            sysLog.setLogModuleId(435);//算是在采集政策接口模块中进行的操作
            sysLog.setLogTime(new Date());
            sysLog.setLogTableNames("zx_zhengCe,zx_zhengCe_piao,zx_zhengCe_xingLi,zx_zhengCe_hangDuan,zx_zhengCe_kuCun");//操作可能会对这些表产生影响
            logService.save(sysLog);
            List<VjResultDataDto> data = resultDto.getData();
            if ( null != data ){
                lccS7SaveDataService.saveOneWay(data,sysLog.getLogId());
            }
        }*/

    }

}
