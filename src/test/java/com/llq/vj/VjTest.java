package com.llq.vj;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 2025/07/22
 **/
public class VjTest {
    public static void main(String[] args) throws Exception {

        File file = new File("d:/a.html");
        FileReader reader = new FileReader(file);

        BufferedReader br = new BufferedReader(reader);

        String line = br.readLine();



       String[] text =  line.split("=================================");

        for (String content: text) {

         /*   String start = "package com.llq.vj.vo;public class ";
            String contStart = content.substring(start.length());
            String fileName = contStart.substring(0,contStart.indexOf(" {"));

            System.out.println(fileName);*/


        }

        String input = "7kg or 10公斤 (Australia/Kazakhstan routes) carry-on baggage";
        Pattern pattern = Pattern.compile("(\\d+)(kg|公斤)");
        Matcher matcher = pattern.matcher(input);

        if(matcher.find()) {
            System.out.println( matcher.group(1));
        } else {
            System.out.println("未找到重量信息");
        }

        if(matcher.find()) {
            System.out.println( matcher.group(1));
        } else {
            System.out.println("未找到重量信息");
        }



    }
}
