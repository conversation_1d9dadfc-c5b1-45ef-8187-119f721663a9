package com.llq.vj;

import com.llq.vj.entity.Lcc_ZhengCe;
import com.llq.vj.pool.Timer_Pool_Lcc_ZhengCe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicInteger;

@SpringBootTest
@Slf4j
class CrawlerVjApplicationMqTests {

    @Resource
    private Timer_Pool_Lcc_ZhengCe pool_lcc_zhengCe;

    AtomicInteger successInteger = new AtomicInteger(0);

   // @Test
    void testS7Request() throws Exception {
        for (int i = 0; i <  1000; i++) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    successInteger.incrementAndGet();
                    pool_lcc_zhengCe.store(new Lcc_ZhengCe());
                }
            }).start();
        }

        long start = System.currentTimeMillis();
        while (true){
            if (successInteger.get() == 1000){
              long cost = System.currentTimeMillis() - start;
              System.out.println(">>>>>>>>>>" + cost);
              break;
           }


        }


    }





}
