package com.llq.vj;

import com.llq.vj.dto.VjResultDataDto;
import com.llq.vj.dto.VjResultDto;
import com.llq.vj.service.Lcc_VjGetDataService;
import com.llq.vj.service.Lcc_VjSaveDataService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/09
 **/
@SpringBootTest
@Slf4j
public class Saas667ApplicationTests_Vj {

    @Resource
    private Lcc_VjGetDataService lccVjGetDataService;
    @Resource
    private Lcc_VjSaveDataService lccVjSaveDataService;

    @Test
    void testS7Request() throws Exception {
        String tripType = "ONE_WAY";
        String depCity = "SGN"; String arrCity = "BKK";
        String departureDate = "2025-10-30";
        String CURRENCY = "THB";
        //String depCity, String arrCity, String departureDate, String currency, int seq, String depAirport, String arrAirport
        VjResultDto dto = lccVjGetDataService.getOneWayVjTicketData(depCity,arrCity,departureDate,CURRENCY,1,depCity,arrCity);


        System.out.println("S7-Test-Ret: " + dto.isSuccess());
        System.out.println("S7-Test-Ret-data: " + dto.getData());

        if (dto.isSuccess()){
            //记录日志,保存政策时一批政策用一个日志，不要一个政策生成一个日志
            List<VjResultDataDto> data = dto.getData();
            if ( null != data ){
                System.out.println(data);
                //lccS7SaveDataService.saveOneWay(data,0L);
            }

        }

    }

    //@Test
    void testS7RequestJson() throws Exception {

        /*StringBuilder content = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new FileReader("D:/json.txt"))) {
            String line;
            while ((line = br.readLine()) != null) {
                content.append(line);
            }
        }
        JSONObject result = JSON.parseObject(content.toString());
        S7DataVo dto =  result.getJSONObject("data").toJavaObject(S7DataVo.class);

        String tripType = "ONE_WAY";
        String depCity = "PKX"; String arrCity = "OVB";
        String departureDate = "2025-08-29";
        String CURRENCY = "RUB";

        VjApiResultVo apiResultVo = new VjApiResultVo();
        apiResultVo.setSuccess(true);
        apiResultVo.setData(dto);
        VjResultDto resultDto = lccS7GetDataService.getOneWayS7TicketData(depCity,arrCity,departureDate,CURRENCY,apiResultVo);


        System.out.println("S7-Test-Ret: " + resultDto.isSuccess());
        System.out.println("S7-Test-Ret-data: " + resultDto.getData());

        if (resultDto.isSuccess()){
            //记录日志,保存政策时一批政策用一个日志，不要一个政策生成一个日志
            List<VjResultDataDto> data = resultDto.getData();
            if ( null != data ){
                lccS7SaveDataService.saveOneWay(data,0L);
            }
        }
*/
    }

}