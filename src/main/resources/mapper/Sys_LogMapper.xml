<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.llq.vj.mapper.Sys_LogMapper">

    <resultMap id="BaseResultMap" type="com.llq.vj.entity.Sys_Log">
            <id property="logId" column="logId" jdbcType="BIGINT"/>
            <result property="logMemberId" column="logMemberId" jdbcType="INTEGER"/>
            <result property="logTime" column="logTime" jdbcType="TIMESTAMP"/>
            <result property="logModuleId" column="logModuleId" jdbcType="INTEGER"/>
            <result property="logTableNames" column="logTableNames" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        logId,logMemberId,logTime,
        logModuleId,logTableNames
    </sql>
</mapper>
