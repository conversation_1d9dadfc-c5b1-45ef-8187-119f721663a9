server:
  port: 8788
  servlet:
    context-path: /vj
  tomcat:
    uri-encoding: UTF-8

spring:
  profiles:
    #  active: dev
    active: prod
  application:
    name: crawler_vj

mybatis-plus:
  #mapper配置文件。可能有多个，所以Mapper.xml结尾的都添加进来
  mapper-locations: classpath*:/mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: off
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      update-strategy: not_null  #更新策略，只更新非空字段

# LCC RUN TOKEN
lcc:
  web:
    token: b231749f3dba463d889c29db8a8f1a41
  vj:
    url: http://107.172.137.183:8070/search
    token: nFMgvhZX00pbj86n0WfBSbIR3OU5yWXmmdtA0A
    connectTimeout: 10000
    readTimeout: 15000
  mail:
    userName: <EMAIL>
    authCode: AFgvvDWVbyaJz5uc
    # 有效期180天 开始日期2025-07-14 截止到期日 2026-01-10


management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus,metrics,threaddump,loggers,beans"
  metrics:
    tags:
      application: ${spring.application.name}
      env: ${spring.profiles.active}