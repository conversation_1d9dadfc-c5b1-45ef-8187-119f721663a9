spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ********************************************************************************************************************************************************************************************************
    #url: ********************************************************************************************************
    username: root
    password: LLq@2025!0825
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      initial-size: 10
      min-idle: 10
      maxActive: 500
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 配置扩展属性，用于监控统计分析SQL性能等
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
  # Mq
  rabbitmq:
    host: ************
    port: 5672
    username: llq
    password: Llq202507#
    virtual-host: /prod
    publisher-returns: false   # 启用返回机制
    exchange: direct.exchange
    #queue: vjQueue
    queue: s7Queue

# LCC RUN SWITCH
lcc:
  vj:
    switch: true
    task:
      core: 60
      max: 72
      queue: 240000
    days: 30
    # 0=1-30 1=31-60 2=61-90
    num: 0
  mail:
    path: /home/<USER>/mail
    server: http://**************:8788/vj