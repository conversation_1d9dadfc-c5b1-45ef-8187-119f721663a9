package com.llq.vj.enums;

import lombok.Getter;

import java.util.Properties;

/**
 * <AUTHOR>
 * @version 2025/07/14
 **/
@Getter
public enum MailHostType {

    TY126("126.com","imap.126.com","imaps",getImap126Congfig()),
    ;


    private String type;
    private String host;
    private String store;
    private Properties prop;

    MailHostType(String type, String host, String store, Properties prop) {
        this.type = type;
        this.host = host;
        this.store = store;
        this.prop = prop;
    }

    /**
     * 获取126邮箱配置
     * @return
     */
    public static Properties getPop126Congfig(){
        Properties props = new Properties();
        props.setProperty("mail.pop3.host", "pop.126.com");
        props.setProperty("mail.pop3.port", "995");
        props.setProperty("mail.pop3.ssl.enable", "true");
        // 保留服务器邮件
        props.setProperty("mail.pop3.rsetbeforequit", "true");
        return props;
    }

    /**
     * 获取126邮箱配置
     * @return
     */
    public static Properties getImap126Congfig(){
        Properties props = new Properties();
        props.put("mail.store.protocol", "imaps");
        props.put("mail.imaps.host", "imap.126.com");
        // 设置连接超时时间为30秒
        props.put("mail.imaps.timeout", "30000");
        // 设置连接超时时间为30秒
        props.put("mail.imaps.connectiontimeout", "30000");
        return props;
    }



}
