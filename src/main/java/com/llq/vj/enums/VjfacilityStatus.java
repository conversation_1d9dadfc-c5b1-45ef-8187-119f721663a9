package com.llq.vj.enums;

/**
 * <AUTHOR>
 * @version 2025/07/10
 **/
public enum VjfacilityStatus {

    PAID("paid","paid"),
    NOTAVAILABLE("notavailable","notavailable"),
    AVAILABLE("available","available"),
    ;

    private String code;
    private String desc;


    VjfacilityStatus(String code, String desc){
        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }
}
