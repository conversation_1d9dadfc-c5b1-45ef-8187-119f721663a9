package com.llq.vj.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @version 2025/07/08
 **/

@Configuration
@Slf4j
public class VjAsyncConfig {


    @Value("${lcc.vj.task.core}")
    private int coreSize;

    @Value("${lcc.vj.task.max}")
    private int maxSize;

    @Value("${lcc.vj.task.queue}")
    private int queueSize;

    /**
     * vj任务执行器
     * @return
     */
    @Bean(name = "VjTaskExecutor")
    public Executor getVjTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(coreSize);
        // 最大线程数
        executor.setMaxPoolSize(maxSize);
        /**
         *
         * 400 * 60 * 10 * 2
         */
        // 队列容量
        executor.setQueueCapacity(queueSize);
        // 线程名前缀
        executor.setThreadNamePrefix("Vj-AsyncTask-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }


    /**
     * 消息队列
     * @return
     */
    @Bean
    public Executor mqExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("RabbitMQ-Async-");
        executor.initialize();
        return executor;
    }


    @PostConstruct
    public void loadConfig(){
        log.info("load Vj schedule config finish！！task:{}",coreSize);
    }
}
