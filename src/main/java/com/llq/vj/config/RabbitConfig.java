package com.llq.vj.config;

import lombok.Getter;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 2025/07/17
 **/
@Configuration
@Getter
public class RabbitConfig {


    //交换机 队列
    @Value("${spring.rabbitmq.exchange}")
    private String exchange;
    @Value("${spring.rabbitmq.queue}")
    private String queue;


    // 定义直连交换机
    @Bean
    public DirectExchange directExchange() {
        return new DirectExchange(exchange);
    }

    // 定义队列
    @Bean
    public Queue vjQueue() {
        // // 参数说明：队列名, 是否持久化, 是否排他, 是否自动删除
        return new Queue(queue, true);
    }

    // 绑定关系
    @Bean
    public Binding binding() {
        return BindingBuilder.bind(vjQueue())
                .to(directExchange()).withQueueName();
    }
}
