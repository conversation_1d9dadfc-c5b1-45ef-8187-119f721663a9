package com.llq.vj.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.llq.vj.entity.LccMailEntity;
import com.llq.vj.mapper.LccMailEntityMapper;
import com.llq.vj.service.LccMailEntityService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【lcc_mail】的数据库操作Service实现
* @createDate 2025-07-12 14:42:03
*/
@Service
public class LccMailEntityServiceImpl extends ServiceImpl<LccMailEntityMapper, LccMailEntity>
    implements LccMailEntityService {

    @Override
    public Date selectMaxReceiveDate(String code) {
        return getBaseMapper().selectMaxReceiveDate(code);
    }
}




