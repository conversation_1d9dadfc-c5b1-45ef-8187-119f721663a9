package com.llq.vj.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.collect.Lists;
import com.llq.vj.config.VjApiConfig;
import com.llq.vj.dto.*;
import com.llq.vj.entity.Lcc_Airline;
import com.llq.vj.enums.AirLineType;
import com.llq.vj.enums.TripType;
import com.llq.vj.http.VjPostUtil;
import com.llq.vj.service.Lcc_AirlineService;
import com.llq.vj.service.Lcc_VjGetDataService;
import com.llq.vj.util.LccDataUtil;
import com.llq.vj.util.LccDateUtil;
import com.llq.vj.vo.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025/07/08
 **/
@Service
@Slf4j
public class Lcc_VjGetDataServiceImpl extends LccBaseServiceImpl implements Lcc_VjGetDataService {

    @Resource
    private VjPostUtil vjPostUtil;
    @Resource
    private Lcc_AirlineService lccAirlineService;

    private final Cache<String, Lcc_Airline> cache = CacheBuilder.newBuilder()
            //60 分钟后过期
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<String, Lcc_Airline>() {
                @Override
                public Lcc_Airline load(String key) {
                    String[] keys = key.split("|");
                    QueryWrapper<Lcc_Airline> queryWrapper = new QueryWrapper<>();
                    queryWrapper.select("DISTINCT depAirport,arrAirport,depCountry,arrCountry")
                            .eq("hangSi", AirLineType.VJ.getCode())
                            .eq("depAirport", keys[1])
                            .eq("arrCountry", keys[2]);
                    List<Lcc_Airline> lccAirlineList = lccAirlineService.list(queryWrapper);
                    return null != lccAirlineList && !lccAirlineList.isEmpty() ?
                            lccAirlineList.get(0) : null;
                }
            });

    /**
     * 抓取航线数据
     *
     * @param depCity
     * @param arrCity
     * @param departureDate
     * @param currency
     * @param seq
     * @param depAirport
     * @param arrAirport
     * @return
     */
    @Override
    public VjResultDto getOneWayVjTicketData(String depCity, String arrCity, String departureDate,
                                             String currency, int seq, String depAirport, String arrAirport) {


        /**
         * 构建查询参数
         *
         * 20250919
         *
         * 查询是机场到机场
         *
         */
        VjDataConDto conDto = VjDataConDto.buildCon(TripType.ONE_WAY.getCode(),
                depAirport, arrAirport,
                departureDate, currency, seq);

        /**
         *
         * 20250811
         * 增加代理IP
         *
         * 采用供应商模式
         *
         *
         * 获取代理地址
         */
       /*String getProxy = QueueUtil.poll();
        if (Strings.isNullOrEmpty(getProxy)) {
            log.error("getVjTicketData-ip:null");
            return  VjResultDto.buildFail("获取代理地址为空");
        }*/
        //传入代理IP指定5模式
        //conDto.getData().setPool(6);
        //getProxy = getProxy.split(",")[0];
        //String[] ipAddr = getProxy.split(":");
        //VjProxyConfigDTO proxyConfigDTO = new VjProxyConfigDTO("luluqi","luluqi",
        //       ipAddr[0],ipAddr[1].replaceAll("\\D",""));

        //user-sub.15946004909-country-us-session-a8qu8b:<EMAIL>:1469
        //VjProxyConfigDTO proxyConfigDTO = VjProxyConfigDTO.getProxy();
        //conDto.getData().setPool(5);
        //conDto.getData().setProxy_config(proxyConfigDTO);

        String json = JSON.toJSONString(conDto);

        log.debug("getVjTicketData-param:{}", json);
        /**
         * 执行查询
         */
        String url = VjApiConfig.getPoolUrl(seq);
        VjApiResultVo resultVo = vjPostUtil.post(url, json);

        return getOneWayVjTicketData(depCity, arrCity, departureDate, currency, resultVo);
    }

    /**
     * 抓取航线数据
     *
     * @param depCity
     * @param arrCity
     * @param departureDate
     * @param currency
     * @return
     */
    @Override
    public VjResultDto getOneWayVjTicketData(String depCity, String arrCity, String departureDate,
                                             String currency, VjApiResultVo apiResultVo) {
        /**
         * 解析返回
         */
        VjResultDto vjResultDto = null;
        if (apiResultVo.isSuccess()) {
            //成功保存数据
            List<VjResultDataDto> vjDataVo = (List<VjResultDataDto>) apiResultVo.getData();
            vjResultDto = VjResultDto.buildOk();
            List<VjResultDataDto> data = vjResultDto.getData();
            data.addAll(vjDataVo);
        } else {
            vjResultDto = VjResultDto.buildFail(apiResultVo.getMsg());
        }
//            List<VjResultDataDto> data = vjResultDto.getData();
//            String departure_list_json = vjDataVo.getDeparture_list();
//            //log.info(">>>>>>>>:{}",departure_list_json);
//            if (!Strings.isNullOrEmpty(departure_list_json)
//                    && departure_list_json.startsWith("{")) {
//                //构建数据
//                buildData(vjDataVo, data, departureDate);
//                //出发地目的地出发日期
//                data.stream().forEach(retDataDto -> {
//                    retDataDto.setDepCity(depCity);
//                    retDataDto.setArrCity(arrCity);
//                });
//            } else {
//                vjResultDto = VjResultDto.buildFail(apiResultVo.getMsg());
//            }
//        } else {
//            log.error("getVjTicketData-post-fail:" + apiResultVo.getMsg());
//            vjResultDto = VjResultDto.buildFail(apiResultVo.getMsg());
//        }
        return vjResultDto;
    }


    /**
     * 保存数据
     *
     * @param vjDataVo
     * @param data
     * @param departureDate
     */
    public void buildData(VjDataVo vjDataVo, List<VjResultDataDto> data, String departureDate) {
        //如果是空值直接返回
        if (null == vjDataVo) {
            data = null;
            return;
        }

        //航程信息
        VjCityPairSearchVo cityPairSearchVO = vjDataVo.getCityPairSearch();


        /**
         *
         *
         *
         * 调试发现han-vcs 2025-10-18 号存在可售航线
         * 但是未返回To_where
         * 只有From_where
         *
         */
        // 调试发现部分数据返回空
        if (null == cityPairSearchVO.getFrom_where()) {
            log.error(">>>buildData-citypair-null:{}", vjDataVo);
            data = null;
            return;
        }

        /**
         *
         *
         *
         * 区分不同产品
         * 航段转换json-list
         *
         *
         */
        String departure_list_json = vjDataVo.getDeparture_list();
        JSONObject departure_list_obj = JSON.parseObject(departure_list_json);
        List<VjDepartureVo> departureVoList = Lists.newArrayList();
        for (String jsonKey : departure_list_obj.keySet()) {
            VjDepartureVo departureVo = departure_list_obj.getObject(jsonKey, VjDepartureVo.class);
            departureVoList.add(departureVo);
        }
        vjDataVo.setDepartureVoList(departureVoList);


        /**
         * 如果无产品
         * 也返回空数据
         */
        if (departureVoList.isEmpty()) {
            data = null;
            return;
        }

        // 循环产品
        for (VjDepartureVo departureVo : departureVoList) {

            /**
             *
             * 如果是非VJ航司
             * 直接返回
             */
            if (!AirLineType.VJ.getCode()
                    .equals(departureVo.getAirline())) {
                continue;
            }

            Boolean canBook = departureVo.getCan_book();
            //如果不可预定直接跳过
            if (!Boolean.TRUE.equals(canBook)) {
                continue;
            }
            VjResultDataDto retDataDto = new VjResultDataDto();
            //航线
            buildDataRoute(cityPairSearchVO, retDataDto, departureDate);
            //获取价格
            buildDataSolution(departureVo, retDataDto);

            if (null != retDataDto.getBasicPriceDto()
                    || null != retDataDto.getStandardPriceDto()) {
                data.add(retDataDto);
            }

        }

    }


    /**
     * 保存航线
     *
     * @param routesVo
     * @param retDataDto    是否是回程
     * @param departureDate
     */
    private void buildDataRoute(VjCityPairSearchVo routesVo, VjResultDataDto retDataDto,
                                String departureDate) {
        /**
         * 航程
         */
        VjWhereVo fromWhereVo = routesVo.getFrom_where();
        // 某些节点不存在，但是存在可售航线，也认为是正常
        VjWhereVo toWhereVo = routesVo.getTo_where();
        String toWhere = null != toWhereVo ? toWhereVo.getCode() : "";

        log.debug("getVjTicketData-flight:departureDate:{},departure:{},arrival:{}",
                departureDate,
                fromWhereVo.getCode(), toWhere);

        retDataDto.setDateStr_go(LccDateUtil.tranDate(departureDate));

    }


    /**
     * 保存解决方案
     *
     * @param departureVo
     * @param retDataDto
     */
    private void buildDataSolution(VjDepartureVo departureVo, VjResultDataDto retDataDto) {
        VjFareTypeVo fareTypeVo = departureVo.getFare_types();
        /**
         * Eco
         */
        VjFareDetailVo basicEconomyVo = fareTypeVo.getEco();
        if (null != basicEconomyVo) {
            String currency_code = basicEconomyVo.getCurrency_code();
            if (!Strings.isNullOrEmpty(currency_code)) {
                VjResultPriceDataDto basicPriceDto = buildPriceDataSolution(departureVo, basicEconomyVo);
                if (null == basicPriceDto) {
                    return;
                }
                retDataDto.setBasicPriceDto(basicPriceDto);
            }
        }
        /**
         * Deluxe
         */
        VjFareDetailVo standardEconomyVo = fareTypeVo.getDeluxe();
        if (null != standardEconomyVo) {
            String currency_code = standardEconomyVo.getCurrency_code();
            if (!Strings.isNullOrEmpty(currency_code)) {
                VjResultPriceDataDto standardPriceDto = buildPriceDataSolution(departureVo, standardEconomyVo);
                if (null == standardPriceDto) {
                    return;
                }
                retDataDto.setStandardPriceDto(standardPriceDto);
            }
        }
    }


    /**
     * 构建产品价格
     *
     * @param basicEconomyVo
     * @return
     */
    private VjResultPriceDataDto buildPriceDataSolution(VjDepartureVo departureVo, VjFareDetailVo basicEconomyVo) {

        /**
         * 构建基本价格
         */
        VjResultPriceDataDto basicPriceDto = new VjResultPriceDataDto();

        // 只获取成人价格
        VjPriceDetailVo vjPriceDetailVo = basicEconomyVo.getPrice_detail();
        //票面价
        BigDecimal exchangeRate = getExchangeRate(basicEconomyVo.getCurrency_code());
        BigDecimal exchangePriceAmount = calcExchangeAmount(exchangeRate, vjPriceDetailVo.getFare_total());
        basicPriceDto.setOriginalCurrency(basicEconomyVo.getCurrency_code());
        basicPriceDto.setExchangeRate(exchangeRate);
        basicPriceDto.setBasePrice(exchangePriceAmount.divide(new BigDecimal(2)));
        String taxFee = vjPriceDetailVo.getTax_fee();
        JSONObject jsonObject = JSONObject.parseObject(taxFee);
        BigDecimal baseAmount = null;
        for (String taxKey : jsonObject.keySet()) {
            JSONObject json = jsonObject.getJSONObject(taxKey);
            String name = json.getString("name");
            if (name.equals("Handling Fee - Fare")) {
                Double baseAmount1 = json.getDouble("baseAmount");
                baseAmount = new BigDecimal(baseAmount1);
            }
        }
        BigDecimal taxFeeTotal = vjPriceDetailVo.getTax_fee_total();
        BigDecimal subtract = taxFeeTotal.subtract(baseAmount);
        //税
        BigDecimal exchangeTaxAmount = calcExchangeAmount(exchangeRate, subtract);
        basicPriceDto.setTaxFeeAount(exchangeTaxAmount.divide(new BigDecimal(2)));

        /**
         * 构建航段信息
         */
        List<VjResultPriceDataSegmentDto> priceDataSegmentDtoList = buildPriceDataSegment(departureVo);
        basicPriceDto.setPriceDataSegmentDtoList_go(priceDataSegmentDtoList);
        for (VjResultPriceDataSegmentDto segmentDto : priceDataSegmentDtoList) {
            buildPriceDataSegmentDetailDto(segmentDto, basicEconomyVo);
        }


        /**
         * 剩余票数
         */
        if (basicEconomyVo.getAvailability() < 1) {
            return null;
        }
        basicPriceDto.setSeatCount(basicEconomyVo.getAvailability());

        /**
         * 退票规则
         *  20250916
         * 跟业务沟通
         * 都不可退票
         */
        basicPriceDto.setNoRefund(1);
        basicPriceDto.setRefundFee(null);

        // 默认无免费行李
        basicPriceDto.setBaggageQuantity(0);
        basicPriceDto.setBaggageWeight(-1);
        // 默认不可改签
        basicPriceDto.setNoChange(1);
        basicPriceDto.setChangeFee(null);

        String content = basicEconomyVo.getContent();
        if (Strings.isNullOrEmpty(content)) {
            return basicPriceDto;
        }

        /**
         *
         * 存在规则
         * 描述
         */
        List<String> allowList = getRuleList(content, true, departureVo);

        /**
         * 行李规则
         */
        List<String> baggageList = allowList.stream()
                .filter(rule -> rule.contains("checked baggage")
                        || rule.contains("托运行李"))
                .collect(Collectors.toList());
        if (!baggageList.isEmpty()) {
            basicPriceDto.setBaggageQuantity(1);
            String baggageDesc = baggageList.get(0);
            Integer baggageWeight = getBaggageQty(baggageDesc).stream()
                    .min(Integer::compareTo)
                    .get();
            basicPriceDto.setBaggageWeight(baggageWeight);
        }

        /**
         * 改签规则
         *  20250916
         * 跟业务沟通
         *
         *
         * 改期费，
         *  货币越南盾
         *  纯越南境内段 改期费350000
         *  国际段：800000
         * 涉及澳大利亚：20000000
         */

        List<String> changeList = allowList.stream()
                .filter(rule -> rule.contains("Change flight date/ time/ route")
                        || rule.contains("Change of flight, date")
                        || rule.contains("允许更改航班、日期或行程"))
                .collect(Collectors.toList());

        String cityPair = departureVo.getCity_pair();
        String[] cityCodes = cityPair.split("-");
        Lcc_Airline lccAirline = this.getRoute(cityCodes[0], cityCodes[1]);

        if (!changeList.isEmpty() && null != lccAirline) {
            basicPriceDto.setNoChange(0);
            // 纯越南境内段 改期费350000
            if (lccAirline.getDepCountry().equals("VN")
                    && lccAirline.getArrCountry().equals("VN")) {
                BigDecimal exchangeRateVND = getExchangeRate("VND");
                BigDecimal changeAmount = calcExchangeAmount(exchangeRateVND, new BigDecimal("350000"));
                basicPriceDto.setChangeFee(changeAmount);
            }
            //涉及澳大利亚：20000000
            else if (lccAirline.getDepCountry().equals("AU")
                    || lccAirline.getArrCountry().equals("AU")) {
                BigDecimal exchangeRateVND = getExchangeRate("VND");
                BigDecimal changeAmount = calcExchangeAmount(exchangeRateVND, new BigDecimal("20000000"));
                basicPriceDto.setChangeFee(changeAmount);
            }
            // 其他国际段
            else {
                BigDecimal exchangeRateVND = getExchangeRate("VND");
                BigDecimal changeAmount = calcExchangeAmount(exchangeRateVND, new BigDecimal("800000"));
                basicPriceDto.setChangeFee(changeAmount);
            }
        }

        return basicPriceDto;
    }


    /**
     * 查询航线为改签提供判断
     *
     * @param depCityCode
     * @param arrCityCode
     * @return
     */
    @SneakyThrows
    private Lcc_Airline getRoute(String depCityCode, String arrCityCode) {

        String cacheKey = new StringBuilder()
                .append(AirLineType.VJ.getCode())
                .append("|")
                .append(depCityCode)
                .append("|")
                .append(arrCityCode)
                .toString();
        return cache.getIfPresent(cacheKey);


    }


    /**
     * 构建航段价格数据
     *
     * @param segmentDto
     * @param basicEconomyVo
     */
    private void buildPriceDataSegmentDetailDto(VjResultPriceDataSegmentDto segmentDto, VjFareDetailVo basicEconomyVo) {
        /**
         *
         * 解析数据中未找到
         * 舱位默认G
         */
        segmentDto.setSeatClass("X");
        segmentDto.setFareBasis(basicEconomyVo.getCode());

    }

    /**
     * 提取数量
     *
     * @param baggageDesc
     */
    private List<Integer> getBaggageQty(String baggageDesc) {
        Pattern pattern = Pattern.compile("(\\d+)(kg|公斤)");
        Matcher matcher = pattern.matcher(baggageDesc);
        List<Integer> list = Lists.newArrayList();
        while (matcher.find()) {
            list.add(Integer.valueOf(matcher.group(1)));
        }
        return list;
    }

    /**
     * 构建允许节点
     * <p>
     * 20250919
     * 沟通业务此节点返回为空
     * 认为不存在此规则
     *
     * @param content
     * @param allow
     * @param departureVo
     */
    private List<String> getRuleList(String content, boolean allow, VjDepartureVo departureVo) {
        Document doc = Jsoup.parse(content);
        Elements elementPs = doc.getElementsByTag("p");
        List<String> ruleList = Lists.newArrayList();
        for (Element elementP : elementPs) {
            Elements elementChildList = elementP.children();
            if (null == elementChildList) {
                log.error(">>>route:{}>>>>>>{}", departureVo, content);
                continue;
            }
            Element imgElement = elementChildList.first();
            if (null == imgElement) {
                log.error(">>>route:{}>>>>>>{}", departureVo, content);
                continue;
            }
            if ("img".equals(imgElement.tagName())) {
                String src = imgElement.attr("src");
                if (src.contains("1606814406304.png")) {
                    Element spanElement = elementChildList.last();
                    ruleList.add(spanElement.text());
                }
            }
        }
        return ruleList;
    }

    /**
     * 构建航段
     *
     * @param departureVo
     * @return
     */
    private List<VjResultPriceDataSegmentDto> buildPriceDataSegment(VjDepartureVo departureVo) {
        List<VjFlightVo> vjFlightVos = departureVo.getFlights();

        List<VjResultPriceDataSegmentDto> priceDataSegmentDtoList = Lists.newArrayList();
        for (VjFlightVo vjFlightVo : vjFlightVos) {
            VjResultPriceDataSegmentDto segmentDto = new VjResultPriceDataSegmentDto();
            segmentDto.setDepAirport(vjFlightVo.getDeparture_code());
            segmentDto.setArrAirport(vjFlightVo.getArrival_code());
            segmentDto.setDepTime(LccDateUtil.tranTime(vjFlightVo.getDeparture_date_html()));
            segmentDto.setArrTime(LccDateUtil.tranTime(vjFlightVo.getArrival_date_html()));
            //航班
            String trimFlightNumber = LccDataUtil.trimLeftZero(vjFlightVo.getFlight_number());
            segmentDto.setFlightNumber(vjFlightVo.getAirline_code() + trimFlightNumber);

            priceDataSegmentDtoList.add(segmentDto);

        }
        return priceDataSegmentDtoList;
    }


}
