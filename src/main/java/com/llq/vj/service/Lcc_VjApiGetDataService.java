package com.llq.vj.service;

import com.llq.vj.dto.VjResultDto;
import com.llq.vj.vo.VjApiResultVo;

/**
 * API
 * 获取数据
 */
public interface Lcc_VjApiGetDataService {

    /**
     * 查询数据
     * @param depCity
     * @param arrCity
     * @param departureDate
     * @param currency
     * @param seq
     * @return
     */
    VjResultDto getOneWayS7TicketData(String depCity, String arrCity, String departureDate,
                                      String currency, int seq);

    VjResultDto getOneWayS7TicketData(String depCity, String arrCity, String departureDate,
                                      String currency, VjApiResultVo apiResultVo);
}
