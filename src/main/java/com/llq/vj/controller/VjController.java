package com.llq.vj.controller;

import com.alibaba.fastjson.JSONObject;
import com.llq.vj.dto.QueryDto;
import com.llq.vj.dto.QueryRetDto;
import com.llq.vj.dto.VjResultDataDto;
import com.llq.vj.dto.VjResultDto;
import com.llq.vj.entity.Lcc_ZhengCe;
import com.llq.vj.payment.VjPayService;
import com.llq.vj.service.Lcc_VjGetDataService;
import com.llq.vj.service.Lcc_VjSaveDataService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/22
 **/
@RestController
public class VjController {


    @Resource
    private Lcc_VjGetDataService getDataService;
    @Resource
    private Lcc_VjSaveDataService saveDataService;
    @Resource
    private Lcc_VjSaveDataService lccVjSaveDataService;
    @Resource
    private VjPayService payService;

    /**
     * 根据条件查询
     * <p>
     * 航段
     *
     * @param queryDto
     */
    @PostMapping(value = "api/flight")
    public QueryRetDto query(@RequestBody QueryDto queryDto) {
        QueryRetDto retDto = new QueryRetDto();
        //如果货币为空默认泰铢
        if (Strings.isEmpty(queryDto.getCurrency())){
            queryDto.setCurrency("THB");
        }
        if (!queryDto.valid()) {
            retDto.setSuccess(false);
            retDto.setMsg("参数不能为空");
            return retDto;
        }

        /**
         * 查询航线数据
         */
        VjResultDto resultDto = getDataService.getOneWayVjTicketData(queryDto.getDepCity(),
                queryDto.getArrCity(), queryDto.getDepartureDate(),
                queryDto.getCurrency(), 1, queryDto.getDepAirport(), queryDto.getArrAirport());
        /**
         * 构建返回
         */
        if (resultDto.isSuccess()) {
            List<VjResultDataDto> data = resultDto.getData();
            if (null != data && !data.isEmpty()) {
                List<Lcc_ZhengCe> zhengCeList = saveDataService.getOneWay(data);
                retDto.setSuccess(true);
                retDto.setData(zhengCeList);
                retDto.setMsg("success");
                /**
                 *
                 * 增加异步回调
                 * 刷新数据
                 */
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        lccVjSaveDataService.saveOneWay(data, 0L);
                    }
                }).start();
            }
        } else {
            retDto.setSuccess(false);
            retDto.setMsg(resultDto.getMsg());
        }

        return retDto;
    }


    /**
     *
     * PNR占座
     * @param paymentInfo
     * @return
     */
    @PostMapping(value = "agent/hold")
    public String hold(@RequestBody String paymentInfo) {

        if (paymentInfo.isEmpty()){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("pnr","");
            jsonObject.put("message","请求参数错误");
            return jsonObject.toString();
        }

        String resultDto = payService.hold(paymentInfo);

        return resultDto;
    }

    @PostMapping(value = "agent/cancel")
    public String cancel(@RequestBody String paymentInfo) {

        if (paymentInfo.isEmpty()){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("message","请求参数错误");
            return jsonObject.toString();
        }

        String resultDto = payService.cancelBooking(paymentInfo);

        return resultDto;
    }


}
