package com.llq.vj.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName lcc_schedule
 */
@TableName(value ="lcc_schedule")
@Data
public class LccSchedule implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 航司
     */
    @TableField(value = "airline")
    private String airline;

    /**
     * 描述
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 开始时间
     */
    @TableField(value = "startTime")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(value = "endTime")
    private Date endTime;

    /**
     * 0=创建，1=运行中，2=已完成
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 秒
     */
    @TableField(value = "costTime")
    private Long costTime;

    /**
     * 日志ID
     */
    @TableField(value = "logId")
    private Long logId;

    /**
     * 日志类型,1为添加，0为修改，-1为删除
     */
    @TableField(value = "logType")
    private Integer logType;

    /**
     * 创建组织ID
     */
    @TableField(value = "createByOrgId")
    private Integer createByOrgId;

    /**
     * 创建者会员ID
     */
    @TableField(value = "createByMemberId")
    private Integer createByMemberId;

    /**
     * 创建时间，修改时也更新
     */
    @TableField(value = "createTime")
    private Date createTime;

    /**
     * 创建时所属租户组织ID
     */
    @TableField(value = "tenantOrgId")
    private Integer tenantOrgId;

    /**
     * 任务数量
     */
    @TableField(value = "taskCount")
    private Integer taskCount;


    /**
     * 成功数量
     */
    @TableField(value = "successCount")
    private Integer successCount;

    /**
     * 成功率
     */
    @TableField(value = "rate")
    private BigDecimal rate;




    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}