package com.llq.vj.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName lcc_mail_attachment
 */
@TableName(value ="lcc_mail_attachment")
@Data
public class LccMailAttachment implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 附件名称
     */
    @TableField(value = "fileName")
    private String fileName;

    /**
     * 文件大小byte
     */
    @TableField(value = "fileSize")
    private Long fileSize;

    /**
     * 路径
     */
    @TableField(value = "fileUrl")
    private String fileUrl;

    /**
     * 邮件ID
     */
    @TableField(value = "mailId")
    private Long mailId;

    /**
     * 创建时间，修改时也更新
     */
    @TableField(value = "createTime")
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}