package com.llq.vj.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * LCC构造政策时传入的航段对象
 */
@Data
public class Lcc_ZhengCe_HangDuan implements Serializable {

    /**
     * 航班号，如：CA123。航班号数字前若有多余的数字0，建议去掉；如CZ006则需返回CZ6。
     */
    private String flightNumber;

    /**
     * 出发机场；IATA 三字码
     */
    private String depAirport;

    /**
     * 到达机场 IATA 三字码
     */
    private String arrAirport;

    /**
     * 起飞日期时间（当地时间），格式支持：YYYYMMDDHHMM或HHMM+1两种形式，如23:50写为2350
     */
    private String depTime;

    /**
     * 到达日期时间（当地时间），格式支持：YYYYMMDDHHMM或HHMM+1两种形式,如第二天03:40写为0340+1
     */
    private String arrTime;

    /**
     * 子舱位，如果采集不到就填"G"就行
     */
    private String seatClass;

    /**
     * 票价等级 1）fareBasis数量必须和航段数一致 2）同一旅行方向的farebasis可以不一致（多段） 3）不同旅行方向farebasis可以不一致 4）每航段1个，使用“ ; ”分割。一张票包含多个航段，一张票有多个fareBasis
     */
    private String fareBasis;

}