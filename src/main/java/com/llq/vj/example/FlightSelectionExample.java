package com.llq.vj.example;

import com.llq.vj.util.FlightSelectionHelper;
import com.llq.vj.util.FlightSelectionHelper.SelectionCriteria;
import com.llq.vj.util.FlightSelectionHelper.FlightDirection;
import com.llq.vj.util.HtmlParserUtil;

/**
 * 航班选择示例
 * 演示如何根据不同条件选择航班参数
 * 
 * <AUTHOR>
 * @version 2025-10-28
 */
public class FlightSelectionExample {
    
    private static final String HTML_FILE_PATH = "src/main/java/com/llq/vj/payment/tast.html";
    
    public static void main(String[] args) {
        System.out.println("=== VietJet 航班选择参数解析示例 ===\n");
        
        // 示例1: 直接查找特定值
        example1_FindSpecificValue();
        
        // 示例2: 选择最便宜的经济舱
        example2_SelectCheapestEconomy();
        
        // 示例3: 选择A1票价代码的航班
        example3_SelectA1FareCode();
        
        // 示例4: 选择价格范围内的航班
        example4_SelectByPriceRange();
        
        // 示例5: 选择返程航班
        example5_SelectReturnFlight();
        
        // 示例6: 获取所有可用选项
        example6_GetAllOptions();
    }
    
    /**
     * 示例1: 直接查找特定值
     */
    private static void example1_FindSpecificValue() {
        System.out.println("1. 查找特定值 '4,A1_ECO,O':");
        
        String targetValue = "4,A1_ECO,O";
        String foundValue = HtmlParserUtil.parseRadioButtonValue(HTML_FILE_PATH, targetValue);
        
        if (foundValue != null) {
            System.out.println("✓ 找到目标值: " + foundValue);
            
            // 获取详细信息
            HtmlParserUtil.FlightOptionInfo info = HtmlParserUtil.parseFlightOptionInfo(HTML_FILE_PATH, targetValue);
            if (info != null) {
                System.out.println("  - 票价: " + info.getFare());
                System.out.println("  - 总价: " + info.getTotalFare());
                System.out.println("  - 费用: " + info.getCharges());
                System.out.println("  - 总费用: " + info.getTotalCompleteCharges());
            }
        } else {
            System.out.println("✗ 未找到目标值");
        }
        System.out.println();
    }
    
    /**
     * 示例2: 选择最便宜的经济舱
     */
    private static void example2_SelectCheapestEconomy() {
        System.out.println("2. 选择最便宜的经济舱航班:");
        
        SelectionCriteria criteria = SelectionCriteria.forEconomyClass();
        criteria.setPriceFirst(true);
        
        String selectedParam = FlightSelectionHelper.selectBestFlight(HTML_FILE_PATH, criteria);
        
        if (selectedParam != null) {
            System.out.println("✓ 选择的参数: " + selectedParam);
            analyzeSelectedFlight(selectedParam);
        } else {
            System.out.println("✗ 未找到符合条件的航班");
        }
        System.out.println();
    }
    
    /**
     * 示例3: 选择A1票价代码的航班
     */
    private static void example3_SelectA1FareCode() {
        System.out.println("3. 选择A1票价代码的航班:");
        
        SelectionCriteria criteria = SelectionCriteria.forFareCode("A1");
        criteria.setCabinClass("ECO");
        
        String selectedParam = FlightSelectionHelper.selectBestFlight(HTML_FILE_PATH, criteria);
        
        if (selectedParam != null) {
            System.out.println("✓ 选择的参数: " + selectedParam);
            analyzeSelectedFlight(selectedParam);
        } else {
            System.out.println("✗ 未找到A1票价代码的航班");
        }
        System.out.println();
    }
    
    /**
     * 示例4: 选择价格范围内的航班
     */
    private static void example4_SelectByPriceRange() {
        System.out.println("4. 选择价格在20-30美元之间的航班:");
        
        SelectionCriteria criteria = SelectionCriteria.forPriceRange(20.0, 30.0);
        criteria.setCabinClass("ECO");
        
        String selectedParam = FlightSelectionHelper.selectBestFlight(HTML_FILE_PATH, criteria);
        
        if (selectedParam != null) {
            System.out.println("✓ 选择的参数: " + selectedParam);
            analyzeSelectedFlight(selectedParam);
        } else {
            System.out.println("✗ 未找到价格范围内的航班");
        }
        System.out.println();
    }
    
    /**
     * 示例5: 选择返程航班
     */
    private static void example5_SelectReturnFlight() {
        System.out.println("5. 选择返程航班:");
        
        SelectionCriteria criteria = new SelectionCriteria();
        criteria.setFlightDirection(FlightDirection.RETURN);
        criteria.setCabinClass("ECO");
        criteria.setPriceFirst(true);
        
        String selectedParam = FlightSelectionHelper.selectBestFlight(HTML_FILE_PATH, criteria);
        
        if (selectedParam != null) {
            System.out.println("✓ 选择的参数: " + selectedParam);
            analyzeSelectedFlight(selectedParam);
        } else {
            System.out.println("✗ 未找到返程航班");
        }
        System.out.println();
    }
    
    /**
     * 示例6: 获取所有可用选项
     */
    private static void example6_GetAllOptions() {
        System.out.println("6. 获取所有可用的航班选项:");
        
        // 获取所有航班选项值
        var flightOptions = HtmlParserUtil.parseFlightOptionValues(HTML_FILE_PATH);
        
        System.out.println("总共找到 " + flightOptions.size() + " 个航班选项:");
        
        // 按票价代码分组显示
        flightOptions.stream()
                .sorted()
                .forEach(option -> {
                    String[] parts = option.split(",");
                    if (parts.length >= 3) {
                        System.out.println("  - " + option + " (索引:" + parts[0] + 
                                         ", 票价代码:" + parts[1] + ", 舱位:" + parts[2] + ")");
                    }
                });
        
        System.out.println();
        
        // 分别获取去程和返程选项
        var depOptions = HtmlParserUtil.parseRadioButtonValuesByName(HTML_FILE_PATH, "gridTravelOptDep");
        var retOptions = HtmlParserUtil.parseRadioButtonValuesByName(HTML_FILE_PATH, "gridTravelOptRet");
        
        System.out.println("去程选项数量: " + depOptions.size());
        System.out.println("返程选项数量: " + retOptions.size());
        System.out.println();
    }
    
    /**
     * 分析选中的航班
     */
    private static void analyzeSelectedFlight(String selectedParam) {
        HtmlParserUtil.FlightOptionInfo info = HtmlParserUtil.parseFlightOptionInfo(HTML_FILE_PATH, selectedParam);
        if (info != null) {
            String[] parts = selectedParam.split(",");
            System.out.println("  - 航班索引: " + parts[0]);
            System.out.println("  - 票价代码: " + parts[1]);
            System.out.println("  - 舱位类型: " + parts[2]);
            System.out.println("  - 基础票价: " + info.getFare());
            System.out.println("  - 总票价: " + info.getTotalFare());
            System.out.println("  - 总费用: " + info.getTotalCompleteCharges());
            
            // 分析票价代码含义
            String fareCode = parts[1];
            String fareCodeMeaning = analyzeFareCode(fareCode);
            System.out.println("  - 票价含义: " + fareCodeMeaning);
        }
    }
    
    /**
     * 分析票价代码含义
     */
    private static String analyzeFareCode(String fareCode) {
        if (fareCode.startsWith("E1")) return "经济舱最低价";
        if (fareCode.startsWith("A1")) return "经济舱优惠价";
        if (fareCode.startsWith("Z1")) return "经济舱中等价";
        if (fareCode.startsWith("W1")) return "经济舱标准价";
        if (fareCode.startsWith("J1")) return "经济舱较高价";
        return "未知票价类型";
    }
    
    /**
     * 实际应用示例：模拟VjPayService中的使用
     */
    public static String getFlightParameterForBooking(String flightNumber, String cabinPreference, Double maxBudget) {
        System.out.println("=== 实际预订场景 ===");
        System.out.println("航班号: " + flightNumber);
        System.out.println("舱位偏好: " + cabinPreference);
        System.out.println("最大预算: " + maxBudget + " USD");
        
        SelectionCriteria criteria = new SelectionCriteria();
        criteria.setCabinClass(cabinPreference);
        if (maxBudget != null) {
            criteria.setMaxPrice(maxBudget);
        }
        criteria.setPriceFirst(true);
        
        String selectedParam = FlightSelectionHelper.selectBestFlight(HTML_FILE_PATH, criteria);
        
        if (selectedParam != null) {
            System.out.println("✓ 为预订选择的参数: " + selectedParam);
            analyzeSelectedFlight(selectedParam);
            return selectedParam;
        } else {
            System.out.println("✗ 未找到符合预订条件的航班");
            return null;
        }
    }
}
