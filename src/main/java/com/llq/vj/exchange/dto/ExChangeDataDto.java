package com.llq.vj.exchange.dto;

import lombok.Data;

/**
 * 功能描述
 *
 * @author: kongwy
 * @date: 2025年07月13日 16:58
 */
@Data
public class ExChangeDataDto {


    /**
     *  "status": "ALREADY",
     *  "scur": "USD", 原币种
     *  "tcur""CNY", 目标币种
     *  "ratenm""美元/人民币",*"rate":"6.5793", 汇率结果(保留6位小数四舍五入)
     *  "update""2016-06-24 08:30:37" 数据更新时间
     **/
    private String status;
    private String scur;
    private String tcur;
    private String ratenm;
    private String rate;
    private String update;


}
