<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>Vietjet预订页面 - 选择航班</title>
    <meta content="Microsoft Visual Studio.NET 7.0" name="GENERATOR"/>
    <meta content="Visual Basic 7.0" name="CODE_LANGUAGE"/>
    <meta content="JavaScript" name="vs_defaultClientScript"/>
    <meta content="http://schemas.microsoft.com/intellisense/ie5" name=
            "vs_targetSchema"/>
    <link rel="stylesheet" href=
            "https://d3dh07ymq581.cloudfront.net/styles/resstyles.css" type="text/css"/>
    <link rel="stylesheet" href=
            "https://d3dh07ymq581.cloudfront.net/styles/tabs-traveloptions.css" type=
                  "text/css"/>
    <link rel="stylesheet" href=
            "https://d3dh07ymq581.cloudfront.net/styles/lang_zh.css" type="text/css"/>
    <script src="https://d3dh07ymq581.cloudfront.net/scripts/booking.summary.js"
            type="text/javascript">
    </script>
    <meta http-equiv='Content-Type' content='text/html; charset=utf-8'/>
    <meta http-equiv='Content-Language' content='en-us'/>
    <script type="text/javascript">
        //<![CDATA[
                var strTermsErr='您必须接受条件和条款后才能继续';

          //]]>
    </script><!-- JavaScript client side validation -->

    <script src="https://d3dh07ymq581.cloudfront.net/scripts/jquery.js" type=
            "text/javascript">
    </script>
    <script language="JavaScript" src=
            "https://d3dh07ymq581.cloudfront.net/scripts/main2.js" type=
                    "text/javascript">
    </script>
    <link rel="stylesheet" type="text/css" href=
            "https://d3dh07ymq581.cloudfront.net/styles/style_min.css"/>
    <!--[if lt IE 7]>
    <script defer type="text/javascript" src="pngfix.js"></script>
    <![endif]-->
    <link rel="stylesheet" href=
            "https://d3dh07ymq581.cloudfront.net/styles/subModal.css" type="text/css"/>
    <script language="JavaScript" type="text/javascript">
        //<![CDATA[
          function SubmitForm() {
           if (CSValidation()) {
                if (typeof(goLoading) != 'undefined') {
                    goLoading();
                }
               pop('?lang=zh');
               setTimeout("document.forms['TravelOptions'].submit()", 100);
           }
          }
          function ResetForm() {
            if (typeof(goLoading) != 'undefined') {
                goLoading();
            }
           pop('?lang=zh');
           setTimeout("document.forms['TravelOptions'].submit()", 100);
          }
          //]]>
    </script>
    <script language="JavaScript" type="text/javascript">
        //<![CDATA[
          try {
          function preventBack(){ if(typeof(allowBackButton) === 'undefined'){window.history.forward();} }
          setTimeout('preventBack()', 0);
          window.onunload=function(){null};
          } catch (error) {}
          //]]>
    </script>
    <script language='JavaScript' type='text/javascript'>
        //<![CDATA[
          <!--
          function CSValidation() {
          try {    var bRv = true;
          } catch(e) {  console.log('Client Validation Failed: ' + e.name + '-' + e.message);  bRv = false;  alert('A javascript error has occurred: ' + e.name + '-' + e.message);}
           return bRv;
          }
          //-->
          //]]>
    </script>
    <script language="JavaScript" type="text/javascript">
        //<![CDATA[
          function SubmitFormRe() {
           if (CSValidationRe()) {
                if (typeof(goLoading) != 'undefined') {
                    goLoading();
                }
               pop('?lang=zh');
               setTimeout("document.forms['TravelOptions'].submit()", 100);
           }
          }
          function ResetFormRe() {
            if (typeof(goLoading) != 'undefined') {
                goLoading();
            }
           pop('?lang=zh');
           setTimeout("document.forms['TravelOptions'].submit()", 100);
          }
          //]]>
    </script>
    <script language="JavaScript" type="text/javascript">
        //<![CDATA[
          try {
          function preventBack(){ if(typeof(allowBackButton) === 'undefined'){window.history.forward();} }
          setTimeout('preventBack()', 0);
          window.onunload=function(){null};
          } catch (error) {}
          //]]>
    </script>
</head>

<body onload="unpop();">
<div id="wrapper">
    <div id="wrapper-2">
        <div id="wrapper3"><img src=
                                        "https://d3dh07ymq581.cloudfront.net/images/bg-footer-zh.png"/></div>

        <div id="container">
            <input type='hidden' name='DebugID' value='C3'/>

            <div id="header">
                <div id="banner">
                    <div class="logo">
                        <a href=
                                   "http://www.vietjetair.com/Sites/Web/en-US/Home"><img alt="logo"
                                                                                         src="https://d3dh07ymq581.cloudfront.net/images/logo.png"/></a>
                    </div>

                    <div class="lang">
                        <select onchange="document.location=this.value">
                            <option value="?lang=vi">
                                tiếng Việt
                            </option>

                            <option value="?lang=en">
                                English
                            </option>

                            <option selected="selected" value="?lang=zh">
                                香港 (简体中文)
                            </option>

                            <option value="?lang=tw">
                                香港 (繁體中文)
                            </option>

                            <option value="?lang=ko">
                                한국의
                            </option>

                            <option value="?lang=th">
                                ภาษาไทย
                            </option>

                            <option value="?lang=ja">
                                日本語
                            </option>

                            <option value="?lang=ru">
                                Русский Язык
                            </option>
                        </select>
                    </div>
                </div><!--End banner-->
            </div><!--End header-->

            <div class="clear" style="height:10px;">
                 
            </div><!-- Progress bar -->

            <div id='step_header'>
                <ul id='new_progress' style=
                        'background-image:url(https://d3dh07ymq581.cloudfront.net/images/new_step_2.gif);'>
                    <li class='wasselected'>日期</li>

                    <li class='isselected'>机票</li>

                    <li class='unselected'>旅客</li>

                    <li class='unselected'>辅营产品</li>

                    <li class='unselected'>付款</li>

                    <li class='unselected'>确认</li>

                    <li class='unselected'>结束</li>
                </ul>
            </div>

            <form method="post" action=
                    "./TravelOptions.aspx?lang=zh&amp;st=sl&amp;sesid=" id="TravelOptions">
                <div class="aspNetHidden">
                    <input type="hidden" name="__VIEWSTATE" id="__VIEWSTATE" value=
                            "/wEPDwUKMTU3NDMxODgyN2RkXzzmaaNNSvJfDubs7QjxY7VQfdY="/>
                </div>

                <div class="aspNetHidden">
                    <input type="hidden" name="__VIEWSTATEGENERATOR" id=
                            "__VIEWSTATEGENERATOR" value="675BBBB2"/>
                </div>
                <input type="hidden" id='button' name='button' value=
                        ''/><input type="hidden" id="InGrandTotalFare" name=
                    "InGrandTotalFare" value=""/> <input type="hidden" id="InGrandTax"
                                                         name="InGrandTax" value=""/> <input type="hidden" id=
                    "InGrandCharges" name="InGrandCharges" value=""/> <input type=
                                                                                     "hidden" id="InGrandTotal"
                                                                             name="InGrandTotal" value=""/>
                <input type="hidden" id="DepSelGrandTotalFare" name=
                        "DepSelGrandTotalFare" value=""/> <input type="hidden" id=
                    "DepSelGrandTax" name="DepSelGrandTax" value=""/> <input type=
                                                                                     "hidden" id="DepSelGrandCharges"
                                                                             name="DepSelGrandCharges" value=
                                                                                     ""/> <input type="hidden"
                                                                                                 id="DepSelGrandTotal"
                                                                                                 name=
                                                                                                         "DepSelGrandTotal"
                                                                                                 value=""/> <input
                    type="hidden" id=
                    "RetSelGrandTotalFare" name="RetSelGrandTotalFare" value=""/>
                <input type="hidden" id="RetSelGrandTax" name="RetSelGrandTax" value=
                        ""/> <input type="hidden" id="RetSelGrandCharges" name=
                    "RetSelGrandCharges" value=""/> <input type="hidden" id=
                    "RetSelGrandTotal" name="RetSelGrandTotal" value=""/>

                <div id="contentwsb" style="float: left; margin-left: 15px;">
                    <span id="XMLSessionMgr"></span> <input type='hidden' name='SesID'
                                                            value=''/><input type='hidden' name='DebugID' value='C3'/>
                    <input type='hidden' name='SesID' value=''/><input type='hidden'
                                                                       name='DebugID' value='C3'/> <input type="hidden"
                                                                                                          id="PN"
                                                                                                          name="PN"
                                                                                                          value=""/>
                    <input type="hidden" id="RPN" name="RPN" value=""/>

                    <p class="xyz" style="display:none;"></p>
                    <br/>

                    <h1>选择旅游方案</h1>
                    <br/>

                    <h2 style=" font-weight: normal; font-size: 12px;"></h2>
                    <br/>

                    <table>
                        <tr>
                            <td></td>

                            <td>QINGDAO HANGTIE NETWORK TECHNOLOGY CO., LTD</td>
                        </tr>

                        <tr>
                            <td></td>

                            <td><span id='AgencyCreditAvailable'>5027.33 USD</span></td>
                        </tr>
                    </table>

                    <h3 style=" font-weight: normal; font-size: 13px;">USD含全部价格 |
                        全部时间按地方机场时间</h3>

                    <div style="height: 20px"></div>

                    <div style="height: 20px"></div>

                    <h1 class="bg1" style="height: 50px; width: 650px;">起程<img src=
                                                                                         "https://d3dh07ymq581.cloudfront.net/images/flightdep.png"/>Ho
                        Chi
                        Minh (SGN) To Beijing Daxing (PKX)</h1>

                    <div id="toDepDiv">
                        <div id="travOpsCat">
                            <table id="gridTravelOptDepHead" cellspacing="0" cellpadding="0"
                                   align="center">
                                <tr>
                                    <td colspan="8"></td>
                                </tr>

                                <tr class="cl-title2">
                                    <td colspan="2">From:
                                        <br/>
                                        <b>Ho Chi Minh</b></td>

                                    <td colspan="2">To:
                                        <br/>
                                        <b>Beijing Daxing</b></td>

                                    <td colspan="4">Search:
                                        <br/>
                                        <b>16/11/2025 Sun</b></td>
                                </tr>

                                <tr>
                                    <td colspan="8"> </td>
                                </tr>

                                <tr class="cl-tab2">
                                    <td colspan="8">
                                        <a class="cl-prev" href=
                                                "javascript:document.forms[&#39;TravelOptions&#39;].RPN.value=&#39;dp&#39;;ResetForm();">
                                            <span>&lt;&lt;</span></a>

                                        <ul class="tabs_ribbon">
                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptDepTab317" href=
                                                        "#">13/11/2025 Thu
                                                    <br/>
                                                    14.86 USD</a>
                                            </li>

                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptDepTab318" href=
                                                        "#">14/11/2025 Fri
                                                    <br/>
                                                    38.86 USD</a>
                                            </li>

                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptDepTab319" href=
                                                        "#">15/11/2025 Sat
                                                    <br/>
                                                    38.86 USD</a>
                                            </li>

                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptDepTab320" href=
                                                        "#">16/11/2025 Sun
                                                    <br/>
                                                    38.86 USD</a>
                                            </li>

                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptDepTab321" href=
                                                        "#">17/11/2025 Mon
                                                    <br/>
                                                    38.86 USD</a>
                                            </li>

                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptDepTab322" href=
                                                        "#">18/11/2025 Tue
                                                    <br/>
                                                    62.86 USD</a>
                                            </li>

                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptDepTab323" href=
                                                        "#">19/11/2025 Wed
                                                    <br/>
                                                    38.86 USD</a>
                                            </li>
                                        </ul>
                                        <a class="cl-Next" href=
                                                "javascript:document.forms[&#39;TravelOptions&#39;].RPN.value=&#39;dn&#39;;ResetForm();"><span>&gt;&gt;</span></a>

                                        <div class="clear spacing"></div>

                                        <table>
                                            <tr valign="top" id="TrvOptGridHdr">
                                                <td style="font-weight: bold" width="80px" align=
                                                        "center" class="Hdr">航班日期
                                                </td>

                                                <td style="font-weight: bold" width="70px" align=
                                                        "center" class="Hdr">从
                                                </td>

                                                <td style="font-weight: bold" width="70px" align=
                                                        "center" class="Hdr">至
                                                </td>

                                                <td style="font-weight: bold" width="80px" align=
                                                        "center" class="Hdr">航班明細
                                                </td>

                                                <td style="font-weight: bold" width="90px" align=
                                                        "center" class="Hdr">ECO
                                                </td>

                                                <td style="font-weight: bold" width="90px" align=
                                                        "center" class="Hdr">DELUXE
                                                </td>

                                                <td style="font-weight: bold" width="90px" align=
                                                        "center" class="Hdr skybossHeader">SKYBOSS
                                                </td>

                                                <td style="font-weight: bold" width="110px" align=
                                                        "center" class="Hdr skybossBusinessHeader">
                                                    BUSINESS
                                                </td>
                                            </tr>
                                        </table>
                                        <hr/>

                                        <div class="panes_ribbon">
                                            <div id="gridTravelOptDepPane317" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptDep1">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">13/11/2025 Thu
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">18:05 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">00:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3948</span>
                                                                                    <br/>
                                                                                    5h 0m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-1-E1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="14.86 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="14.86 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "89.05 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                    "gridTravelOptDep"
                                                                                            value="1,E1_ECO,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;E1_ECO&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    14.86 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-1-A1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="49.15 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="49.15 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "123.34 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="1,A1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;A1_DLX&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    49.15 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-1-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="18:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="74.19 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "625.87 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                    "gridTravelOptDep"
                                                                                            value="1,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>

                                            <div id="gridTravelOptDepPane318" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptDep2">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">14/11/2025 Fri
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">18:05 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">00:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3948</span>
                                                                                    <br/>
                                                                                    5h 0m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-2-Z1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="38.86 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="38.86 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "113.05 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="2,Z1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;Z1_ECO&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    38.86 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-2-Z1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="64.77 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="64.77 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "138.96 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="2,Z1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;Z1_DLX&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    64.77 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-2-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="18:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="74.19 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "625.87 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                    "gridTravelOptDep"
                                                                                            value="2,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>

                                            <div id="gridTravelOptDepPane319" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptDep3">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">15/11/2025 Sat
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">18:05 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">00:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3948</span>
                                                                                    <br/>
                                                                                    5h 0m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-3-Z1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="38.86 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="38.86 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "113.05 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="3,Z1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;Z1_ECO&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    38.86 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-3-Z1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="64.77 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="64.77 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "138.96 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="3,Z1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;Z1_DLX&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    64.77 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-3-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="18:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="74.19 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "625.87 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                    "gridTravelOptDep"
                                                                                            value="3,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>

                                            <div id="gridTravelOptDepPane320" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptDep4">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">16/11/2025 Sun
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">18:05 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">00:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3948</span>
                                                                                    <br/>
                                                                                    5h 0m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-4-Z1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="38.86 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="38.86 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "113.05 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="4,Z1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;Z1_ECO&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    38.86 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-4-Z1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="64.77 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="64.77 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "138.96 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="4,Z1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;Z1_DLX&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    64.77 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-4-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="18:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="74.19 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "625.87 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                    "gridTravelOptDep"
                                                                                            value="4,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>

                                            <div id="gridTravelOptDepPane321" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptDep5">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">17/11/2025 Mon
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">18:05 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">00:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3948</span>
                                                                                    <br/>
                                                                                    5h 0m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-5-Z1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="38.86 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="38.86 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "113.05 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="5,Z1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;Z1_ECO&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    38.86 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-5-Z1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="64.77 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="64.77 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "138.96 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="5,Z1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;Z1_DLX&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    64.77 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-5-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="18:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="74.19 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "625.87 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                    "gridTravelOptDep"
                                                                                            value="5,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>

                                            <div id="gridTravelOptDepPane322" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptDep6">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">18/11/2025 Tue
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">18:05 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">00:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3948</span>
                                                                                    <br/>
                                                                                    5h 0m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-6-J1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="62.86 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="62.86 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "137.05 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="6,J1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;J1_ECO&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    62.86 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-6-J1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="95.25 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="95.25 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "169.44 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="6,J1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;J1_DLX&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    95.25 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-6-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="18:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="74.19 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "625.87 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                    "gridTravelOptDep"
                                                                                            value="6,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>

                                            <div id="gridTravelOptDepPane323" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptDep7">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">19/11/2025 Wed
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">18:05 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">00:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3948</span>
                                                                                    <br/>
                                                                                    5h 0m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-7-Z1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="38.86 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="38.86 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "113.05 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="7,Z1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;Z1_ECO&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    38.86 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-7-Z1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="64.77 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="64.77 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="18:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="74.19 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "138.96 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                   "gridTravelOptDep"
                                                                                           value="7,Z1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;Z1_DLX&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    64.77 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptDep-7-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="18:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="74.19 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "625.87 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptDep" name=
                                                                                                    "gridTravelOptDep"
                                                                                            value="7,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;d&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <input type="hidden" name="OperatedBy" id="OperatedBy" value=
                                ""/>
                    </div>

                    <div style="height: 40px"></div>

                    <h1 class="bg1" style="height: 50px; width: 650px;">回程<img src=
                                                                                         "https://d3dh07ymq581.cloudfront.net/images/flightret.png"/>Beijing
                        Daxing (PKX) To Ho Chi Minh (SGN)</h1>

                    <div id="toRetDiv">
                        <div id="travOpsCat">
                            <table id="gridTravelOptRetHead" cellspacing="0" cellpadding="0"
                                   align="center">
                                <tr>
                                    <td colspan="8"></td>
                                </tr>

                                <tr class="cl-title2">
                                    <td colspan="2">From:
                                        <br/>
                                        <b>Beijing Daxing</b></td>

                                    <td colspan="2">To:
                                        <br/>
                                        <b>Ho Chi Minh</b></td>

                                    <td colspan="4">Search:
                                        <br/>
                                        <b>17/11/2025 Mon</b></td>
                                </tr>

                                <tr>
                                    <td colspan="8"> </td>
                                </tr>

                                <tr class="cl-tab2">
                                    <td colspan="8">
                                        <a class="cl-prev" href=
                                                "javascript:document.forms[&#39;TravelOptions&#39;].RPN.value=&#39;rp&#39;;ResetForm();">
                                            <span>&lt;&lt;</span></a>

                                        <ul class="tabs_ribbon">
                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptRetTab318" href=
                                                        "#">14/11/2025 Fri
                                                    <br/>
                                                    26.29 USD</a>
                                            </li>

                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptRetTab319" href=
                                                        "#">15/11/2025 Sat
                                                    <br/>
                                                    38.86 USD</a>
                                            </li>

                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptRetTab320" href=
                                                        "#">16/11/2025 Sun
                                                    <br/>
                                                    62.86 USD</a>
                                            </li>

                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptRetTab321" href=
                                                        "#">17/11/2025 Mon
                                                    <br/>
                                                    26.29 USD</a>
                                            </li>

                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptRetTab322" href=
                                                        "#">18/11/2025 Tue
                                                    <br/>
                                                    46.48 USD</a>
                                            </li>

                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptRetTab323" href=
                                                        "#">19/11/2025 Wed
                                                    <br/>
                                                    46.48 USD</a>
                                            </li>

                                            <li>
                                                <a class="tabEnabled" id="gridTravelOptRetTab324" href=
                                                        "#">20/11/2025 Thu
                                                    <br/>
                                                    46.48 USD</a>
                                            </li>
                                        </ul>
                                        <a class="cl-Next" href=
                                                "javascript:document.forms[&#39;TravelOptions&#39;].RPN.value=&#39;rn&#39;;ResetForm();"><span>&gt;&gt;</span></a>

                                        <div class="clear spacing"></div>

                                        <table>
                                            <tr valign="top" id="TrvOptGridHdr">
                                                <td style="font-weight: bold" width="80px" align=
                                                        "center" class="Hdr">航班日期
                                                </td>

                                                <td style="font-weight: bold" width="70px" align=
                                                        "center" class="Hdr">从
                                                </td>

                                                <td style="font-weight: bold" width="70px" align=
                                                        "center" class="Hdr">至
                                                </td>

                                                <td style="font-weight: bold" width="80px" align=
                                                        "center" class="Hdr">航班明細
                                                </td>

                                                <td style="font-weight: bold" width="90px" align=
                                                        "center" class="Hdr">ECO
                                                </td>

                                                <td style="font-weight: bold" width="90px" align=
                                                        "center" class="Hdr">DELUXE
                                                </td>

                                                <td style="font-weight: bold" width="90px" align=
                                                        "center" class="Hdr skybossHeader">SKYBOSS
                                                </td>

                                                <td style="font-weight: bold" width="110px" align=
                                                        "center" class="Hdr skybossBusinessHeader">
                                                    BUSINESS
                                                </td>
                                            </tr>
                                        </table>
                                        <hr/>

                                        <div class="panes_ribbon">
                                            <div id="gridTravelOptRetPane318" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptRet1">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">14/11/2025 Fri
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">01:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">05:10 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3949</span>
                                                                                    <br/>
                                                                                    5h 5m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-1-A1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="26.29 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="26.29 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "101.53 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="1,A1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;A1_ECO&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    26.29 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-1-A1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="49.15 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="49.15 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "124.39 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="1,A1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;A1_DLX&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    49.15 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-1-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="01:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="75.24 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "626.92 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                    "gridTravelOptRet"
                                                                                            value="1,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>

                                            <div id="gridTravelOptRetPane319" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptRet2">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">15/11/2025 Sat
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">01:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">05:10 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3949</span>
                                                                                    <br/>
                                                                                    5h 5m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-2-Z1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="38.86 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="38.86 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "114.10 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="2,Z1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;Z1_ECO&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    38.86 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-2-Z1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="64.77 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="64.77 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "140.01 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="2,Z1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;Z1_DLX&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    64.77 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-2-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="01:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="75.24 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "626.92 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                    "gridTravelOptRet"
                                                                                            value="2,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>

                                            <div id="gridTravelOptRetPane320" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptRet3">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">16/11/2025 Sun
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">01:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">05:10 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3949</span>
                                                                                    <br/>
                                                                                    5h 5m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-3-J1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="62.86 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="62.86 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "138.10 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="3,J1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;J1_ECO&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    62.86 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-3-J1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="95.25 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="95.25 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "170.49 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="3,J1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;J1_DLX&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    95.25 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-3-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="01:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="75.24 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "626.92 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                    "gridTravelOptRet"
                                                                                            value="3,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>

                                            <div id="gridTravelOptRetPane321" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptRet4">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">17/11/2025 Mon
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">01:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">05:10 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3949</span>
                                                                                    <br/>
                                                                                    5h 5m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-4-A1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="26.29 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="26.29 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "101.53 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="4,A1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;A1_ECO&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    26.29 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-4-A1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="49.15 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="49.15 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "124.39 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="4,A1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;A1_DLX&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    49.15 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-4-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="01:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="75.24 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "626.92 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                    "gridTravelOptRet"
                                                                                            value="4,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>

                                            <div id="gridTravelOptRetPane322" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptRet5">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">18/11/2025 Tue
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">01:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">05:10 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3949</span>
                                                                                    <br/>
                                                                                    5h 5m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-5-W1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="46.48 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="46.48 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "121.72 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="5,W1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;W1_ECO&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    46.48 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-5-W1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="72.39 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="72.39 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "147.63 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="5,W1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;W1_DLX&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    72.39 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-5-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="01:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="75.24 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "626.92 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                    "gridTravelOptRet"
                                                                                            value="5,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>

                                            <div id="gridTravelOptRetPane323" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptRet6">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">19/11/2025 Wed
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">01:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">05:10 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3949</span>
                                                                                    <br/>
                                                                                    5h 5m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-6-W1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="46.48 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="46.48 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "121.72 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="6,W1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;W1_ECO&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    46.48 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-6-W1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="72.39 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="72.39 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "147.63 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="6,W1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;W1_DLX&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    72.39 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-6-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="01:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="75.24 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "626.92 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                    "gridTravelOptRet"
                                                                                            value="6,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>

                                            <div id="gridTravelOptRetPane324" class="pane" width=
                                                    "100%">
                                                <table cellspacing="0" cellpadding="0" align="center">
                                                    <tr class="cl-borderLR">
                                                        <td colspan="8">
                                                            <!-- Flights-->

                                                            <table cellspacing="0" cellpadding="0" class=
                                                                    "FlightsGrid cl-fixw100">
                                                                <tr class="gridFlightEven" id=
                                                                        "gridTravelOptRet7">
                                                                    <td colspan="4">
                                                                        <table width="300px" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">20/11/2025 Thu
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">01:05 PKX
                                                                                    <br/>
                                                                                    Beijing Daxing
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo">05:10 SGN
                                                                                    <br/>
                                                                                    Ho Chi Minh
                                                                                </td>

                                                                                <td width="110px" style=
                                                                                        "padding:0px 1px" align="center"
                                                                                    class=
                                                                                            "SegInfo"><span class=
                                                                                                                    "airlineVJ">VJ3949</span>
                                                                                    <br/>
                                                                                    5h 5m
                                                                                    <br/>
                                                                                    <span class="flightOperated">Operated
                                        by
                                        <br/>
                                        Vietjet</span></td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>

                                                                    <td colspan="4">
                                                                        <table class="FaresGrid" cellspacing="0"
                                                                               cellpadding="0">
                                                                            <tr class="gridFlightEven">
                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-7-W1_ECO-O"
                                                                                    data-familyid="Eco"><input type=
                                                                                                                       "hidden"
                                                                                                               id="fare"
                                                                                                               value="46.48 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="46.48 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "121.72 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="7,W1_ECO,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;W1_ECO&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    46.48 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-7-W1_DLX-O"
                                                                                    data-familyid="Deluxe"><input type=
                                                                                                                          "hidden"
                                                                                                                  id="fare"
                                                                                                                  value="72.39 USD"/>
                                                                                    <input type="hidden" id="fare_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="charge_taxes" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_fare"
                                                                                            value="72.39 USD"/>
                                                                                    <input type="hidden" id="depTime"
                                                                                           value="01:05"/> <input
                                                                                            type="hidden"
                                                                                            id="charges"
                                                                                            value="75.24 USD"/>
                                                                                    <input type="hidden" id="infcharge"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden" id="inftax"
                                                                                            value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                            value=
                                                                                                    "147.63 USD"/>
                                                                                    <input type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                   "gridTravelOptRet"
                                                                                           value="7,W1_DLX,O"
                                                                                           onclick=
                                                                                                   "toORChk(&#39;W1_DLX&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    72.39 USD
                                                                                </td>

                                                                                <td width="90px" align="center" id=
                                                                                        "gridTravelOptRet-7-Y_SBoss-O"
                                                                                    data-familyid="SkyBoss"><input type=
                                                                                                                           "hidden"
                                                                                                                   id="fare"
                                                                                                                   value=
                                                                                                                           "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="fare_taxes"
                                                                                           value="0.00 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="charge_taxes"
                                                                                           value="0.00 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="total_fare" value=
                                                                                                    "551.68 USD"/>
                                                                                    <input type="hidden"
                                                                                           id="depTime" value="01:05"/>
                                                                                    <input type="hidden" id="charges"
                                                                                           value="75.24 USD"/> <input
                                                                                            type=
                                                                                                    "hidden"
                                                                                            id="infcharge" value=
                                                                                                    "0.00 USD"/> <input
                                                                                            type="hidden" id=
                                                                                            "inftax" value="0.00 USD"/>
                                                                                    <input type="hidden" id=
                                                                                            "total_complete_charges"
                                                                                           value=
                                                                                                   "626.92 USD"/> <input
                                                                                            type="radio" id=
                                                                                            "gridTravelOptRet" name=
                                                                                                    "gridTravelOptRet"
                                                                                            value="7,Y_SBoss,O"
                                                                                            onclick=
                                                                                                    "toORChk(&#39;Y_SBoss&#39;,&#39;O&#39;,&#39;r&#39;);SelectTravOpt(this);"/>
                                                                                    551.68 USD
                                                                                </td>

                                                                                <td width="90px" align="center" valign=
                                                                                        "middle">售完
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>

                                                                <tr>
                                                                    <td colspan="8">
                                                                        <hr/>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <br/>

                    <div style="height: 25px;">
                         
                    </div>

                    <div class="title_operatedby">
                        Operated by: <span class=
                                                   "title_operatedby_VJ">VJ-VietjetAir</span><span class=
                                                                                                           "title_operatedby_VT">VZ - Thai Vietjet</span>
                    </div>
                    <br/>

                    <div>
                        <a class="button rightbutton" href=
                                "javascript:document.forms[&#39;TravelOptions&#39;].button.value=&#39;continue&#39;;SubmitForm();">
                            继续</a> <a class="button rightbutton" href=
                            "javascript:document.forms[&#39;TravelOptions&#39;].button.value=&#39;back&#39;;ResetForm();">
                        返回</a> <a class="button rightbutton" href=
                            "javascript:document.forms[&#39;TravelOptions&#39;].button.value=&#39;agentmenu&#39;;ResetForm();">
                        菜单页面</a>
                    </div>

                    <div class="clear"></div>
                    <img border='0' id='indAgencyLogin' name=
                            'indAgencyLogin' src='images/key.gif' alt='Logged In'/></div>
                <!-- id="contentwsb" -->

                <div id="sidebar" style="float: right; margin-right: 10px;">
                    <div class="round_top"></div>

                    <div class="round_mid">
                        <h1 class="title_travinfo" style=
                                "font-size: 22px; margin: 0; padding: 0; text-align: center">
                            预订总结</h1>

                        <div class="detail_travinfo">
                            <p><span class="label">含全部价格</span> US Dollar
                                <br/>
                                <br/></p>

                            <h2>起飞航班</h2>

                            <table id='tblLeg1APs'>
                                <tr>
                                    <td><span class='label'>从:</span> Ho Chi Minh   </td>

                                    <td><span class='label'>至:</span> Beijing Daxing</td>
                                </tr>
                            </table>

                            <table id='tblLeg1Info'>
                                <tr>
                                    <td><span class='label'>出发:</span> <span id=
                                                                                       'Leg1Date'>16/11/2025 Sun</span>
                                        <span id=
                                                      'Leg1Time'></span></td>
                                </tr>

                                <tr id='Leg1BookingSummary' style='display:none;'>
                                    <td>
                                        <span class='label'>票价:</span> <span id=
                                                                                       'Leg1BSFare'></span>
                                        <br/>

                                        <div>
                                            <span class='label'>计费:</span> <span id=
                                                                                           'Leg1BSCharges'></span>
                                            <br/>
                                            <span class='label'>税务:</span> <span id=
                                                                                           'Leg1BSFareTax'></span>
                                            <br/>
                                            <span class='label'>总计:</span> <span id=
                                                                                           'Leg1BSTotalFare'></span>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            <br/>

                            <h2>回程航班</h2>

                            <table id='tblLeg2APs'>
                                <tr>
                                    <td><span class='label'>从:</span> Beijing Daxing   </td>

                                    <td><span class='label'>至:</span> Ho Chi Minh</td>
                                </tr>
                            </table>

                            <table id='tblLeg2Info'>
                                <tr>
                                    <td><span class='label'>回程日期:</span> <span id=
                                                                                           'Leg2Date'>17/11/2025 Mon</span>
                                        <span id=
                                                      'Leg2Time'></span></td>
                                </tr>

                                <tr id='Leg2BookingSummary' style='display:none;'>
                                    <td>
                                        <span class='label'>票价:</span> <span id=
                                                                                       'Leg2BSFare'></span>
                                        <br/>

                                        <div>
                                            <span class='label'>计费:</span> <span id=
                                                                                           'Leg2BSCharges'></span>
                                            <br/>
                                            <span class='label'>税务:</span> <span id=
                                                                                           'Leg2BSFareTax'></span>
                                            <br/>
                                            <span class='label'>总计:</span> <span id=
                                                                                           'Leg2BSTotalFare'></span>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            <br/>
                            <span class="label_grand">总共:</span> <span id="BSGrandTotal"
                                                                         class="BSGrandTotal">0</span> <span class=
                                                                                                                     "BSGrandTotal">USD</span>

                            <h2 class="h2_passengers_title">旅客数量</h2>

                            <table id="tblPaxCountsInfo">
                                <tr>
                                    <td><span class="label">大人:</span> 1  </td>

                                    <td><span class="label">儿童:</span> 0  </td>

                                    <td><span class="label">婴儿:</span> 0</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="round_bottom"></div>
                </div><!-- sidebar -->

                <div class="clear"></div>

                <div style="height: 30px"></div>

                <div id="footer">
                    <p style="padding-top:60px;">© 2014 Vietjet Air Copy Right. All
                        Rights Reserved.
                        <br/>
                        <br/>
                        <span class="lnk"><a rel="noreferrer noopener" style=" color: red;"
                                             href="http://www.beian.miit.gov.cn" target=
                                                     "_blank">粤ICP备19097418号-2</a></span></p>

                    <p class="author"></p>

                    <div class="clear"></div>
                </div><!--End footer-->

                <div class="clear"></div>
            </form>
        </div><!-- id="container" -->

        <div class="clear"></div>
    </div><!--End wrapper-2-->

    <div class="clear"></div>
</div><!--End wrapper-->
<style>
    <![CDATA[
      .modal {
        display:    none;
        position:   fixed;
        z-index:    1000;
        top:        0;
        left:       0;
        height:     100%;
        width:      100%;
        background: rgba( 255, 255, 255, .0 )
                    url('https://d3dh07ymq581.cloudfront.net/images/wait_bg.png')
                    50% 50%
                    no-repeat;
      }
      body.loading {
        overflow: hidden;
      }
      body.loading .modal {
        display: block;
      }
      ]]>
</style>

<div class="modal">
    <table height="100%" width="100%" style="z-index:1001;">
        <tbody>
        <tr>
            <td style="vertical-align:middle;">
                <h1 class="loadingPopupHeading" style="text-align:center;">
                    请稍候。。。</h1>

                <p style="text-align:center;padding-top:25px;"><img src=
                                                                            "https://d3dh07ymq581.cloudfront.net/images/loading11.gif"/>
                </p>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<script language="JavaScript" type="text/javascript">
    //<![CDATA[
        if($('.ErrorCaption').text().length > 0) {
            // Checking the text inside a div

            // Condition to check the  text match
            if($('.ErrorCaption').text().indexOf('目前不存在您规范的航班。')){
               // console.log('Hello');
                $('.xyz').css('display','block');
            }

        }



        if (document.forms['TravelOptions'].ZeroOptions) {
            if (gE('invis') != null) {
                gE('invis').style.display = 'none';
            }
        }
        if (document.forms['TravelOptions'].ServerBusy) {
            if (gE('invis') != null) {
                gE('invis').style.display = 'none';
            }
            if (gE('head') != null) {
                gE('head').style.display = 'none';
            }
        }


        $(function () {
            $("#gridTravelOptDepHead ul.tabs_ribbon").tabs("div.panes_ribbon > div", {
                initialIndex: 3
                });




            $("#gridTravelOptRetHead ul.tabs_ribbon").tabs("div.panes_ribbon > div", {
                initialIndex: 3
                });



        });


        var prev_selection = "";
        var prev_selection2 = "";
        function SelectTravOpt(elem) {
            var sFields = elem.value.split(",");
            if (elem.name == "gridTravelOptDep") {
                if (prev_selection != "") { document.getElementById(prev_selection).style.background = "#FFF"; }
            }
            else {
                if (prev_selection2 != "") { document.getElementById(prev_selection2).style.background = "#FFF"; }
            }
            document.getElementById(elem.name + "-" + sFields[0] + "-" + sFields[1] + "-" + sFields[2]).style.background = "#E51F20";
            if (elem.name == "gridTravelOptDep") {
               prev_selection = elem.name + "-" + sFields[0] + "-" + sFields[1] + "-" + sFields[2];
            }
            else {
                prev_selection2 = elem.name + "-" + sFields[0] + "-" + sFields[1] + "-" + sFields[2];
            }
        }


        $(document).ready(function () {
        /* select service level */
            $("#lstLvlService option[value=1]").prop("selected", true)
        /* dynamic sidebar population */
            $('input:radio').click(function () {
                var gridType = '';
                var fare = 0;
                var fareVal = "";
                var fareTaxe = 0;
                            var chargeTaxe = 0;
                var totalFare = 0;
                var depTime = '';
                var charges = 0;
                var grandTotal = 0;
                var otherTotalFare = 0;
                var sCurrency = "";
                var PerInfantCharges = 0;
                var PerInfantTaxes = 0;
                var start, end;
                if ($(this).parent().children('#fare').val().replace(/,/gi, '').split(" ").length > 1)
                {
                    sCurrency = $(this).parent().children('#fare').val().replace(/,/gi, '').split(" ")[1];
                }

                gridType = $(this).attr('id');
                fare = $(this).parent().children('#fare').val().replace(/,/gi, '').split(" ")[0];
                fareVal = $(this).parent().children('#fare_taxes').val();
                start = fareVal.indexOf(">");
                end = fareVal.indexOf("<\/span>");
                if(start > 0)
                    fareVal = fareVal.substring(start+1, end);
                fareTaxe = fareVal.replace(/,/gi, '').split(" ")[0];
                chargeTaxe = $(this).parent().children('#charge_taxes').val().replace(/,/gi, '').split(" ")[0];
                depTime = $(this).parent().children('#depTime').val().replace(/,/gi, '').split(" ")[0];
                charges = $(this).parent().children('#charges').val().replace(/,/gi, '').split(" ")[0];
                PerInfantCharges = $(this).parent().children('#infcharge').val().replace(/,/gi, '').split(" ")[0];
                PerInfantTaxes = $(this).parent().children('#inftax').val().replace(/,/gi, '').split(" ")[0];

                grandTotal = $(this).parent().children('#total_complete_charges').val().replace(/,/gi, '').split(" ")[0];

                var moneyType = booking.getMoneyType(fare);
                var depDate = $('#gridTravelOptDepHead a.current').text();
                var retDate = $('#gridTravelOptRetHead a.current').text();
                var cnt = 0;
                depDate = depDate.replace(/ /g, function (match, i, original) {
                    cnt++;
                    return (cnt === 2) ? "]" : match;
                });
                depDate = depDate.substring(0, depDate.indexOf(']'));
                cnt = 0;
                retDate = retDate.replace(/ /g, function (match, i, original) {
                    cnt++;
                    return (cnt === 2) ? "]" : match;
                });
                retDate = retDate.substring(0, retDate.indexOf(']'));



                if (moneyType.sign > 0) {
                    fare = fare.replace(String.fromCharCode(moneyType.sign), '');
                    charges = charges.replace(String.fromCharCode(moneyType.sign), '');
                    fareTaxe = fareTaxe.replace(String.fromCharCode(moneyType.sign), '');
                                    chargeTaxe = chargeTaxe.replace(String.fromCharCode(moneyType.sign), '');
                    PerInfantCharges = PerInfantCharges.replace(String.fromCharCode(moneyType.sign), '');
                    PerInfantTaxes = PerInfantTaxes.replace(String.fromCharCode(moneyType.sign), '');
                    grandTotal = grandTotal.replace(String.fromCharCode(moneyType.sign), '');
                }

                if (typeof (gridType) != 'undefined') {
                    if (gridType.indexOf('Dep') > -1) {

                        var total_pax;

                            total_pax = 1 + 0;

                        var totalpaxfare = fare * total_pax;
                        var taxes = fareTaxe;
                        var totalinfno;

                            totalinfno = 0;

                        var totalinfanttaxes = totalinfno * PerInfantTaxes;
                        var totalinfantcharges = totalinfno * PerInfantCharges;

                        var totaltaxes = Number(taxes) + Number(chargeTaxe) + Number(totalinfanttaxes);
                        var totalchrges = Number(charges) + Number(totalinfantcharges);
                        $('#Leg1BookingSummary').show();
                        $('#Leg1BSFare').html('').append(moneyType.toMoneyString(totalpaxfare) + " " + sCurrency);
                        $('#Leg1BSFareTax').html('').append(moneyType.toMoneyString(totaltaxes) + " " + sCurrency);
                        $('#Leg1BSTotalFare').html('').append(moneyType.toMoneyString(grandTotal) + " " + sCurrency);
                        $('#Leg1BSCharges').html('').append(moneyType.toMoneyString(totalchrges) + " " + sCurrency);
                        $('span#Leg1Time').html('').text(depTime);
                        $('span#Leg1Date').html('').text(depDate);

                       // console.log(taxes);
                       // console.log(totaltaxes);

                    }
                    else if (gridType.indexOf('Ret') > -1) {
                        var total_pax;

                            total_pax = 1 + 0;

                        var totalpaxfare = fare * total_pax;
                        var taxes = fareTaxe;
                        var totalinfno;

                            totalinfno = 0;

                        var totalinfanttaxes = totalinfno * PerInfantTaxes;
                        var totalinfantcharges = totalinfno * PerInfantCharges;

                        var totaltaxes = Number(taxes) + Number(chargeTaxe) + Number(totalinfanttaxes);
                        var totalchrges = Number(charges) + Number(totalinfantcharges);
                        $('#Leg2BookingSummary').show();
                        $('#Leg2BSFare').html('').append(moneyType.toMoneyString(totalpaxfare) + " " + sCurrency);
                        $('#Leg2BSFareTax').html('').append(moneyType.toMoneyString(totaltaxes) + " " + sCurrency);
                        $('#Leg2BSTotalFare').html('').append(moneyType.toMoneyString(grandTotal) + " " + sCurrency);
                        $('#Leg2BSCharges').html('').append(moneyType.toMoneyString(totalchrges) + " " + sCurrency);
                        $('span#Leg2Time').html('').text(depTime);
                        $('span#Leg2Date').html('').text(retDate);
                    }
                    // grand total
                    booking.updateGrandTotal();
                }
            });
            var sSharedAirline = "Operated by<br>" + " " + "Vietjet";
            for (var i = 0; i < $(".flightOperated").length; i++) {
                if ($(".flightOperated:eq("+i+")").html() == sSharedAirline) {
                    $(".flightOperated:eq(" + i + ")").hide();
                }
                else {
                    $(".flightOperated:eq(" + i + ")").show();
                }
            }
        });
      //]]>
</script>

<form>
    <input type='hidden' name='DebugID' value='C3'/>
</form>
<style type="text/css">
    /*<![CDATA[*/
        #fareRules1Box, #fareRules2Box, #fareRules3Box, #fareRules4Box, #fareRules5Box,
        #fareRules6Box, #fareRules7Box, #fareRules8Box, #fareRules9Box, #fareRules10Box {
            display: none; position: absolute; top: 450px; }
        .fareRulesMOver1, .fareRulesMOver2, .fareRulesMOver3, .fareRulesMOver4, .fareRulesMOver5,
        .fareRulesMOver6, .fareRulesMOver7, .fareRulesMOver8, .fareRulesMOver9, .fareRulesMOver10 {
            display: none;
            width: 300px;
            background-color: #FFF;
            -webkit-box-shadow: 2px 3px 5px 0px rgba(0,0,0,0.7);
            -moz-box-shadow: 2px 3px 5px 0px rgba(0,0,0,0.7);
            box-shadow: 2px 3px 5px 0px rgba(0,0,0,0.7);
            border: 1px solid rgba(0,0,0,0.5);
        }
        .fareHeader { background-color: rgb(229, 31, 32); padding: 8px 5px; color: #FFF; text-align: center; font-weight: bold; font-size: 18px; }
        .whatInclu { background-color: rgb(254, 239, 24); padding: 8px 5px; font-weight: bold; font-size: 10px; }
        .fareRulesContent { background-color: #FFF; padding: 4px; }
        .fareRulesContent ul { list-style-position: outside; margin-left: 10px; }
        .fareRulesContent ul li { padding: 2px; }
        .fareRulesContent p { margin: 8px 0 3px 0; }
        .fareRulesContent a { font-weight: bold; color: rgb(229, 31, 32); }
        .greencheckmark { list-style-image: url(./images/checkmark_green_16x16.png); }
        .redx { list-style-image: url(./images/x_red_16x16.png);}
        .closeX { float: right; padding-right: 6px; padding-top: 4px; }
        .closeX a { color: #000; cursor: pointer; }
      /*]]>*/
</style>

<div id="fareRules1Box"></div>

<div id="fareRules2Box"></div>

<div id="fareRules3Box"></div>

<div id="fareRules4Box"></div>

<div id="fareRules5Box"></div>

<div id="fareRules6Box"></div>

<div id="fareRules7Box"></div>

<div id="fareRules8Box"></div>

<div id="fareRules9Box"></div>

<div id="fareRules10Box"></div>

<div class="fareRulesMOver" id="ecofarerules" familyid="1">
    <div class="fareHeader">
        ECO
    </div>

    <div class="whatInclu">
        包含:
    </div>

    <div class="fareRulesContent">
        <ul>
            <li class="greencheckmark">7 公斤手提行李</li>
        </ul>
    </div>

    <div class="whatInclu">
        未包含:
    </div>

    <div class="fareRulesContent">
        <ul>
            <li class="redx">托运行李（可选）</li>

            <li class="redx">热食</li>

            <li class="redx">便利套装</li>

            <li class="redx">选座</li>

            <li class="redx">更改航班，日期，行程</li>

            <li class="redx">更改乘客姓名</li>

            <li class="redx">票价差额（如果有）</li>
        </ul>
    </div>
</div>

<div class="fareRulesMOver" id="deluxefarerules" familyid="2">
    <div class="fareHeader">
        DELUXE
    </div>

    <div class="whatInclu">
        包含:
    </div>

    <div class="fareRulesContentDeluxe">
        <div class="fareRulesContent">
            <ul>
                <li class="greencheckmark">根据航线适用 7 公斤或 10 公斤手提行李</li>

                <li class="greencheckmark">根据航线免费20 公斤或 40 公斤的托运行李</li>

                <li class="greencheckmark">餐饮适用于飞行时间7小时以上的航线</li>

                <li class="greencheckmark">优先选座（SkyBoss 座位除外）</li>

                <li class="greencheckmark">允许更改航班、日期或行程（收取票价差额，如有）</li>
            </ul>
        </div>

        <div class="whatInclu">
            未包含:
        </div>

        <div class="fareRulesContent">
            <ul>
                <li class="redx">便利套装</li>

                <li class="redx">乘客更改姓名</li>

                <li class="redx">优先办理登机手续</li>
            </ul>
        </div>
    </div>

    <div class="fareRulesContentCodeShare fareRulesContent">
        <ul>
            <li class="greencheckmark">7kgs carry on baggage.</li>

            <li class="greencheckmark">20kgs checked baggage.</li>

            <li class="greencheckmark">Meal ( Snack only).</li>
        </ul>
    </div>
</div>

<div class="fareRulesMOver" id="skybossfarerules" familyid="6">
    <div class="fareHeader">
        SKYBOSS
    </div>

    <div class="whatInclu">
        包含:
    </div>

    <div class="fareRulesContent">
        <ul>
            <li class="greencheckmark">根据航线适用 10 公斤或 14 公斤手提行李 <b>(Flights to and
                from Con Dao 7kg)</b></li>

            <li class="greencheckmark">根据航线免费30 公斤或 50 公斤的托运行李和 01（一）套高尔夫球具（如有）
                <b>(Flights to and from Con Dao 7kg)</b></li>

            <li class="greencheckmark">使用豪华贵宾室（不适用于泰国国内航班和没有标准贵宾室服务的机场）</li>

            <li class="greencheckmark">优先办理登机手续</li>

            <li class="greencheckmark">优先行李处理服务</li>

            <li class="greencheckmark">安检优先（取决于机场条件和设施）</li>

            <li class="greencheckmark">私人接驳车到飞机旁（机场无登机桥情况下）</li>

            <li class="greencheckmark">选座优先</li>

            <li class="greencheckmark">机上提供免费食品和饮料</li>

            <li class="greencheckmark">便利套装（4小时以上的航班）</li>

            <li class="greencheckmark">机票退费实名制保留款，从预计起飞日起算2年内有效</li>
        </ul>
    </div>
</div>

<div class="fareRulesMOver" id="skybosspremierfarerules" familyid="7">
    <div class="fareHeader">
        BUSINESS
    </div>

    <div class="whatInclu">
        包含:
    </div>

    <div class="fareRulesContent">
        <ul>
            <li class="greencheckmark">18 公斤手提行李</li>

            <li class="greencheckmark">根据航线免费40 公斤或 60 公斤的托运行李和
                01（一）套高尔夫球具（如有）
            </li>

            <li class="greencheckmark">使用豪华贵宾室（不适用于泰国国内航班和没有标准贵宾室服务的机场）</li>

            <li class="greencheckmark">优先办理登机手续</li>

            <li class="greencheckmark">优先行李处理服务</li>

            <li class="greencheckmark">安检优先（取决于机场条件和设施）</li>

            <li class="greencheckmark">私人接驳车到飞机旁（机场无登机桥情况下）</li>

            <li class="greencheckmark">优先选座</li>

            <li class="greencheckmark">机上提供的免费食品和饮料</li>

            <li class="greencheckmark">便利套装（4小时以上的航班）</li>

            <li class="greencheckmark">机票退费实名制保留款，从预计起飞日起算2年内有效</li>
        </ul>
    </div>
</div>
<script type="text/javascript">
    //<![CDATA[
        var fareMaps = { 'eco': 1, 'deluxe': 2, 'skyboss': 6, 'skybosspremier': 7 };

        $(function () {
            var $valueViewer = false;
            var $travelOptions = false;
            var $leftPos = 0;
            var $topPos = 0;
            var $toPosSpacing = 45;

            var $travelOptionIndex = 0;
            var $travelOptionCount = 0;
            var $travelOptionDiv = undefined;
            var $travelOptionTable = undefined;
            var $Fields;

            $('.fareRulesMOver').each(function () { // clone the fare rule boxes into the travel option boxes
                var farBox = $(this);
                for (var i = 1; i < 11; i++) {
                    farBox.clone()
                            .attr('id', $(this).attr('id') + i)
                            .attr('class', $(this).attr('class') + i)
                            .appendTo('#fareRules' + i + 'Box');
                }
                $(this).remove(); // don't need the element anymore, remove it
            });

            $('.closeX a').click(function () {
                $(this).parent().parent().fadeOut(150);
            });

            if ($('#toDepDiv').length) {
                $travelOptions = true;
                if ($('#toRetDiv').length) {
                    $travelOptionCount = 2;
                } else {
                    $travelOptionCount = 1;
                }
            } else if ($('#vvDepDiv').length) {
                $valueViewer = true;
                if ($('#vvRetDiv').length) {
                    $travelOptionCount = 2;
                } else {
                    $travelOptionCount = 1;
                }
            } else {
                $travelOptionCount = 10;
            }


            for (var i = 1; i <= $travelOptionCount; i++) {
                if ($valueViewer == true) {
                    $travelOptionTable = 'table';
                    $travelOptionDiv = '#vvDepDiv';
                    if (i > 1) { $travelOptionDiv = '#vvRetDiv'; }
                } else {
                    $travelOptionTable = 'table.FaresGrid';
                    if ($travelOptions == true) {
                        $travelOptionDiv = '#toDepDiv';
                        if (i > 1) { $travelOptionDiv = '#toRetDiv'; }
                    } else {
                        $travelOptionDiv = '#toMultiDiv';
                    }
                }

                $($travelOptionDiv + ' ' + $travelOptionTable + ' tbody tr td')
                    .each(function () {
                        $(this).mouseenter(function () {
                            if ($(this).attr('data-familyid') != undefined) {

                                $Fields = $(this).attr('id').split("-");

                                if (!parseInt($Fields[0].substr($Fields[0].search('Opt') + 3, $Fields[0].length - $Fields[0].search('Opt') + 3))) {
                                    if ($Fields[0].indexOf('Dep') > -1) {
                                        $travelOptionIndex = 1
                                    } else {
                                        $travelOptionIndex = 2
                                    }
                                } else {
                                    $travelOptionIndex = $Fields[0].substr($Fields[0].search('Opt') + 3, $Fields[0].length - $Fields[0].search('Opt') + 3);
                                }

                                var famId;
                                if ($valueViewer == true) {
                                    famId = $(this).attr('data-familyid')
                                } else {
                                    famId = getFareFamId($(this).attr('data-familyid').toLowerCase());
                                }

                                $leftPos = $(this).offset().left;
                                $topPos = $(this).offset().top + $toPosSpacing;

                                $('#fareRules' + $travelOptionIndex + 'Box')
                                                .css('left', $leftPos)
                                                .css('top', $topPos);

                                for (var j = 1; j <= $travelOptionCount; j++) {
                                    $('#fareRules' + j + 'Box > div.fareRulesMOver' + j).hide();
                                }
                                //$('#fareRules' + i + 'Box > div.fareRulesMOver' + i).hide();
                                                            var counter = $(this).attr('id').split("-");
                                                            var num = parseInt(counter[1])-1;
                                                            if($("span.flightOperated:eq("+num+")").is(":hidden")){
                                                                    $(".fareRulesContentCodeShare").hide();
                                                                    $(".fareRulesContentDeluxe").removeClass("fareRulesContentNonCodeShare");
                                                                    $("#OperatedBy").val("");
                                                            }
                                                            else{
                                                                    $(".fareRulesContentCodeShare").show();
                                                                    $(".fareRulesContentDeluxe").addClass("fareRulesContentNonCodeShare");
                                                                    $("#OperatedBy").val("1");
                                                            }
                                $('#fareRules' + $travelOptionIndex + 'Box > div.fareRulesMOver' + $travelOptionIndex + '[familyid=' + famId + ']').show();

                            }
                        });
                    });

                if (($valueViewer == true) || ($travelOptions == true) || (i == 1)) {

                    $($travelOptionDiv).mouseleave(function () {
                        for (var j = 1; j <= $travelOptionCount; j++) {
                            $('#fareRules' + j + 'Box > div.fareRulesMOver' + j).hide();
                        }
                    });
                }
            }



            $('#fareRules1Box').show(); // show the fare rules boxes
            $('#fareRules2Box').show();
            $('#fareRules3Box').show();
            $('#fareRules4Box').show();
            $('#fareRules5Box').show();
            $('#fareRules6Box').show();
            $('#fareRules7Box').show();
            $('#fareRules8Box').show();
            $('#fareRules9Box').show();
            $('#fareRules10Box').show();
        });

        function getFareFamId(s) {
            // eco
            if (s.indexOf('eco') > -1) {
                return fareMaps.eco;
            }
            // deluxe
                    if (s.indexOf('deluxe') > -1) {
                return fareMaps.deluxe;
            }
                    // promo
            if (s.indexOf('skyboss') > -1) {
                if (s.indexOf('business') > -1) {
                    return fareMaps.skybosspremier;
                }
                return fareMaps.skyboss;
            }
                    // plus
            if (s.indexOf('skybossbusiness') > -1 || s.indexOf('business') > -1) {
                return fareMaps.skybosspremier;
            }
            return 0;
        }
      //]]>
</script>
<script type="text/javascript">
    //<![CDATA[
            (function(){
                var s   = document.createElement('script');
                var x   = document.getElementsByTagName('script')[0];
                s.type  = 'text/javascript';
                s.async = true;
                s.src   = ('https:'==document.location.protocol?'https://':'http://')
                        + 'ap-sonar.sociomantic.com/js/2010-07-01/adpan/vietjetair-vn';
                x.parentNode.insertBefore( s, x );
            })();
      //]]>
</script>
<script>
    <![CDATA[
      window.dataLayer = window.dataLayer || [];

      ]]>
</script> <!-- Google Tag Manager -->
<noscript>
    <iframe src="//www.googletagmanager.com/ns.html?id=GTM-WXHJMR"
            height="0" width="0" style=
                    "display:none;visibility:hidden"></iframe>
</noscript>
<script>
    <![CDATA[
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-WXHJMR');
      ]]>
</script> <!-- End Google Tag Manager -->
</body>
</html>
