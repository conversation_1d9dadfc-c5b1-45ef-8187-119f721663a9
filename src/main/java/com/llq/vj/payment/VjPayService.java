package com.llq.vj.payment;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.base.Strings;
import com.llq.vj.http.VjHttpUtil;
import com.llq.vj.util.CommonUtil;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.Credentials;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.conn.params.ConnRoutePNames;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.CoreConnectionPNames;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.net.HttpURLConnection;
import java.nio.charset.Charset;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.llq.vj.http.VjHttpUtil.readHtmlContentFromEntity;
import static com.llq.vj.util.CommonUtil.*;
import static com.llq.vj.util.proxyUtil.getProxyIP;


@Service
public class VjPayService {
    static Logger logger = LoggerFactory.getLogger(VjPayService.class);


    String proxy_host = "";
    int proxy_port = 0;
    String proxy_user = "";
    String proxy_pass = "";

    String userName = "CH330346A18KXM";
    String passWord = "200306Zhc!";



    private String viewState = "";
    private String viewStateEgenerator = "";
    private String sesID = "";
    private String debugID = "";


    public static void main(String[] args) {
        String paymentInfo = "{\n" +
                "    \"adult\": 1,\n" +
                "    \"airline_code\": \"S7\",\n" +
                "    \"child\": 1,\n" +
                "    \"contact\": {\n" +
                "        \"address\": \"\",\n" +
                "        \"area_code\": \"+86\",\n" +
                "        \"city\": \"BEIJING\",\n" +
                "        \"country\": \"CN\",\n" +
                "        \"email\": \"<EMAIL>\",\n" +
                "        \"first_name\": \"XU\",\n" +
                "        \"last_name\": \"BIN\",\n" +
                "        \"mobile\": \"13969746091\",\n" +
                "        \"name\": \"BIN/XU\",\n" +
                "        \"post_code\": \"\"\n" +
                "    },\n" +
                "    \"currency_code\": \"RMB\",\n" +
                "    \"infant\": 0,\n" +
                "    \"order_no\": \"123456789\",\n" +
                "    \"passengers\": [\n" +
                "        {\n" +
                "            \"birthday\": \"1984-01-25\",\n" +
                "            \"country\": \"CN\",\n" +
                "            \"expiration_time\": \"2028-05-29\",\n" +
                "            \"first_name\": \"YUANBO\",\n" +
                "            \"gender\": \"MALE\",\n" +
                "            \"last_name\": \"DONG\",\n" +
                "            \"name\": \"DONG/YUANBO\",\n" +
                "            \"passportNO\": \"*********\",\n" +
                "            \"passport_country\": \"CN\",\n" +
                "            \"type\": \"ADULT\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"birthday\": \"2015-01-25\",\n" +
                "            \"country\": \"CN\",\n" +
                "            \"expiration_time\": \"2028-05-30\",\n" +
                "            \"first_name\": \"NINGNING\",\n" +
                "            \"gender\": \"MALE\",\n" +
                "            \"last_name\": \"CHEN\",\n" +
                "            \"name\": \"CHEN/NINGNING\",\n" +
                "            \"passportNO\": \"*********\",\n" +
                "            \"passport_country\": \"CN\",\n" +
                "            \"type\": \"CHILD\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"baggages\": [\n" +
                "    ],\n" +
                "    \"pnr\": \"NF17VJ\",\n" +
                "    \"routing\": {\n" +
                "        \"fromSegments\": [\n" +
//                "            {\n" +
//                "                \"arr\": \"IKT\",\n" +
//                "                \"arr_time\": \"************\",\n" +
//                "                \"cabin\": \"V\",\n" +
//                "                \"dep\": \"PKX\",\n" +
//                "                \"dep_time\": \"************\",\n" +
//                "                \"flightNumber\": \"S76312\"\n" +
//                "            },\n" +
                "            {\n" +
                "                \"arr\": \"HAN\",\n" +
                "                \"arr_time\": \"************\",\n" +
                "                \"cabin\": \"V\",\n" +
                "                \"dep\": \"KIX\",\n" +
                "                \"dep_time\": \"************\",\n" +
                "                \"flightNumber\": \"VJ931\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        VjPayService vjPayService = new VjPayService();
        vjPayService.pay(paymentInfo);
    }


    public String cancelBooking(String bookingInfo) {

        return null;
    }


    public String hold(String paymentInfo) {


        return null;
    }

    public String pay(String paymentInfo) {

        JSONObject resultJSON = new JSONObject();

        JSONObject paymentJSON = JSONObject.parseObject(paymentInfo);

        String currency = paymentJSON.getString("currency_code");

        int adult = paymentJSON.getIntValue("adult");
        int child = paymentJSON.getIntValue("child");
        int infant = paymentJSON.getIntValue("infant");

        JSONArray passengers = paymentJSON.getJSONArray("passengers");
        String order_no = paymentJSON.getString("order_no");

        logger = LoggerFactory.getLogger(VjPayService.class + order_no);

        JSONObject routing = paymentJSON.getJSONObject("routing");

        String dep = routing.getJSONArray("fromSegments").getJSONObject(0).getString("dep");
        String arr = routing.getJSONArray("fromSegments").getJSONObject(routing.getJSONArray("fromSegments").size() - 1).getString("arr");

        String re_dep = "";
        String re_arr = "";

        if (routing.containsKey("retSegments")) {
            re_dep = routing.getJSONArray("retSegments").getJSONObject(0).getString("dep");
            re_arr = routing.getJSONArray("retSegments").getJSONObject(routing.getJSONArray("retSegments").size() - 1).getString("arr");
        }

        String dep_time = routing.getJSONArray("fromSegments").getJSONObject(0).getString("dep_time");
        String arr_time = routing.getJSONArray("fromSegments").getJSONObject(routing.getJSONArray("fromSegments").size() - 1).getString("arr_time");

        String re_dep_time = "";
        String re_arr_time = "";

        if (routing.size() > 1) {
            re_dep = routing.getJSONArray("retSegments").getJSONObject(0).getString("dep_time");
            re_arr = routing.getJSONArray("retSegments").getJSONObject(routing.getJSONArray("retSegments").size() - 1).getString("arr_time");
        }

        String flightNumber = routing.getJSONArray("fromSegments").getJSONObject(0).getString("flightNumber");
        String cabin = "";

        DefaultHttpClient client = VjHttpUtil.getHttpClient("127.0.0.1:8888");


        try {
            X509TrustManager xtm = new X509TrustManager() { // 创建TrustManager
                @Override
                public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                }

                @Override
                public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                }

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
            };
            // TLS1.0与SSL3.0基本上没有太大的差别，可粗略理解为TLS是SSL的继承者，但它们使用的是相同的SSLContext
            SSLContext ctx = null;
            try {

                ctx = SSLContext.getInstance("TLS");
                // 使用TrustManager来初始化该上下文，TrustManager只是被SSL的Socket所使用
                ctx.init(null, new TrustManager[]{xtm}, null);
                // 创建SSLSocketFactory
                SSLSocketFactory socketFactory = new SSLSocketFactory(ctx);
                // 通过SchemeRegistry将SSLSocketFactory注册到我们的HttpClient上
                client.getConnectionManager().getSchemeRegistry().register(new Scheme("https", 443, socketFactory));
                // 设置超时时间
                client.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, 30 * 1000);
                client.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 6000 * 5);

            } catch (NoSuchAlgorithmException e) {

                throw new RuntimeException(e);
            } catch (KeyManagementException e) {

                throw new RuntimeException(e);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

//        String getProxy = getProxyIP(client);
//        if (Strings.isNullOrEmpty(getProxy)) {
//            return "ERROR_getIp失败";
//        }
//
//        String[] split = getProxy.split(":");
//
//        proxy_host = split[0];
//        proxy_port = Integer.parseInt(split[1].replaceAll("\r", "").replaceAll("\n", ""));
//        proxy_user = "luluqi";
//        proxy_pass = "luluqi";
//
//
//        proxy_ip(client);

        String getLoginParam = getLoginParam(client);
        if (getLoginParam.contains("ERROR_")){
            resultJSON.put("pnr", "");
            resultJSON.put("message", getLoginParam);
            return resultJSON.toString();
        }

        String login = login(client,userName,passWord);
        if (login.contains("ERROR_")){
            resultJSON.put("pnr", "");
            resultJSON.put("message", login);
            return resultJSON.toString();
        }

        String agentOptions = agentOptions(client,currency);
        if (agentOptions.contains("ERROR_")){
            resultJSON.put("pnr", "");
            resultJSON.put("message", agentOptions);
            return resultJSON.toString();
        }

        String sign = getSign(agentOptions);
        if (sign.contains("ERROR_")){
            resultJSON.put("pnr", "");
            resultJSON.put("message", sign);
            return resultJSON.toString();
        }

        // 查询航班
        String searchFlight = searchFlight(client, currency, sign,dep,arr,dep_time,re_dep_time,adult,child,infant);
        if (searchFlight.contains("ERROR_")){
            resultJSON.put("pnr", "");
            resultJSON.put("message", searchFlight);
            return resultJSON.toString();
        }

        // 选择航班
        String travelOptions = travelOptions(client, searchFlight,dep_time,arr_time,re_dep_time,re_arr_time,flightNumber);
        if (travelOptions.contains("ERROR_")){
            resultJSON.put("pnr", "");
            resultJSON.put("message", travelOptions);
            return resultJSON.toString();
        }
        logger.info("选择航班成功");

//        // 添加乘客
//        String addPassenger = addPassenger(request, passengerList);
//        if (sign.contains("ERROR_")){
//            resultJSON.put("pnr", "");
//            resultJSON.put("message", sign);
//            return resultJSON.toString();
//        }
//        logger.info("添加乘客成功");
//
//        // 添加辅营
//        String addOnes = addOnes(request, passengerList);
//        if (sign.contains("ERROR_")){
//            resultJSON.put("pnr", "");
//            resultJSON.put("message", sign);
//            return resultJSON.toString();
//        }
//
//        // 获取 订单的支付费用
//        String payTotalPrice = payTotalPrice(info);
//        if (sign.contains("ERROR_")){
//            resultJSON.put("pnr", "");
//            resultJSON.put("message", sign);
//            return resultJSON.toString();
//        }
        return "";
    }

    public void getTravelOptions(DefaultHttpClient client) {
        HttpGet httpGet = new HttpGet("https://agents.vietjetair.com//TravelOptions.aspx?lang=en&st=sl&sesid=");
        httpGet.addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        httpGet.addHeader("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");
        httpGet.addHeader("cache-control", "no-cache");
        httpGet.addHeader("pragma", "no-cache");
        httpGet.addHeader("priority", "u=0, i");
        httpGet.addHeader("referer", "https://agents.vietjetair.com/ViewFlights.aspx?lang=en&st=sl&sesid=");
        httpGet.addHeader("sec-ch-ua", "\"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        httpGet.addHeader("sec-ch-ua-mobile", "?0");
        httpGet.addHeader("sec-ch-ua-platform", "\"Windows\"");
        httpGet.addHeader("sec-fetch-dest", "document");
        httpGet.addHeader("sec-fetch-mode", "navigate");
        httpGet.addHeader("sec-fetch-site", "same-origin");
        httpGet.addHeader("sec-fetch-user", "?1");
        httpGet.addHeader("upgrade-insecure-requests", "1");
        httpGet.addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36");
        try {
            HttpResponse response = client.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == HttpURLConnection.HTTP_OK) {
                String info = readHtmlContentFromEntity(response.getEntity());
                getQueryParam(info);
            } else {
                String info = readHtmlContentFromEntity(response.getEntity());
                logger.info("ERROR_getSearchFlight失败,response:"+info);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String travelOptions(DefaultHttpClient client, String info,String depTime,String arrTime ,String re_depTime,String re_arrTime, String flightNumber) {
        getTravelOptions(client);
        HttpPost httpPost = new HttpPost("https://agents.vietjetair.com//TravelOptions.aspx?lang=en&st=sl&sesid=");
        httpPost.addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        httpPost.addHeader("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");
        httpPost.addHeader("cache-control", "no-cache");
        httpPost.addHeader("content-type", "application/x-www-form-urlencoded");
        httpPost.addHeader("origin", "https://agents.vietjetair.com");
        httpPost.addHeader("pragma", "no-cache");
        httpPost.addHeader("priority", "u=0, i");
        httpPost.addHeader("referer", "https://agents.vietjetair.com//TravelOptions.aspx?lang=en&st=sl&sesid=");
        httpPost.addHeader("sec-ch-ua", "\"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        httpPost.addHeader("sec-ch-ua-mobile", "?0");
        httpPost.addHeader("sec-ch-ua-platform", "\"Windows\"");
        httpPost.addHeader("sec-fetch-dest", "document");
        httpPost.addHeader("sec-fetch-mode", "navigate");
        httpPost.addHeader("sec-fetch-site", "same-origin");
        httpPost.addHeader("sec-fetch-user", "?1");
        httpPost.addHeader("upgrade-insecure-requests", "1");
        httpPost.addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36");


        try {
            String fromDate = CommonUtil.yyyyMMddTOddmmyyyy(depTime);

            String reFlightKey = "";
            if (!re_depTime.isEmpty()) {
                String reDate = CommonUtil.yyyyMMddTOddmmyyyy(re_depTime);
                String mmSs = getMmSs(reDate);
                reFlightKey = getFlightKey(info, flightNumber, re_depTime,re_arrTime, reDate, mmSs);
                if (reFlightKey.contains("ERROR")) {
                    return "ERROR_获取返程航班key 失败";
                }
            }

            String mmSs = getMmSs(depTime);
            String flightKey = getFlightKey(info, flightNumber, depTime,arrTime, fromDate, mmSs);
            if (flightKey.contains("ERROR")) {
                return "ERROR_获取去程航班key 失败";
            }

            List<NameValuePair> formData = new ArrayList<NameValuePair>();
            formData.add(new BasicNameValuePair("__VIEWSTATE", viewState));
            formData.add(new BasicNameValuePair("__VIEWSTATEGENERATOR", viewStateEgenerator));
            formData.add(new BasicNameValuePair("button", "continue"));
            formData.add(new BasicNameValuePair("SesID", sesID));
            formData.add(new BasicNameValuePair("DebugID", debugID));
            formData.add(new BasicNameValuePair("SesID", sesID));
            formData.add(new BasicNameValuePair("DebugID", debugID));
            formData.add(new BasicNameValuePair("PN", ""));
            formData.add(new BasicNameValuePair("RPN", ""));
            formData.add(new BasicNameValuePair("gridTravelOptDep", flightKey));
            formData.add(new BasicNameValuePair("OperatedBy", ""));
            if (StringUtils.isNotBlank(reFlightKey)){
                formData.add(new BasicNameValuePair("gridTravelOptRet", reFlightKey));
            }

            String format = URLEncodedUtils.format(formData, Charset.forName("UTF-8"));
            httpPost.setEntity(new StringEntity(format));
            HttpResponse response1 = client.execute(httpPost);
            String html = readHtmlContentFromEntity(response1.getEntity());
            if (response1.getStatusLine().getStatusCode() == 200 || response1.getStatusLine().getStatusCode() == 201) {
                getQueryParam(html);
                return html;
            } else {
                logger.info("ERROR_选择航班失败");
                return "ERROR_选择航班失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR_选择航班失败";
        }
    }

    public String getFlightKey(String info, String flightNumber, String depTime,String arrTime, String date, String mmSs) {
        Document document = Jsoup.parse(info);
        Elements spans;
        if (flightNumber.contains("VZ")){
            spans = document.select("span.airlineVZ:contains(" + flightNumber.split("/")[0] + ")");
        }else {
            spans = document.select("span.airlineVJ:contains(" + flightNumber.split("/")[0] + ")");
        }

        if (!spans.isEmpty()) {
            for (Element span : spans) {
                Element tr = span.parent().parent().parent().parent().parent();
                // 航班信息 用于校验用
                String flightInfo = tr.text();

                if (flightNumber.contains("/")){
                    String arrMmSs = getMmSs(depTime);
                    String twoMmSs = getMmSs(arrTime);
                    String twoArrMmSs = getMmSs(arrTime);
                    if (flightInfo.contains(flightNumber.split("/")[0]) && flightInfo.contains(flightNumber.split("/")[1])
                            && flightInfo.contains(date) && flightInfo.contains(mmSs) && flightInfo.contains(arrMmSs)
                            && flightInfo.contains(twoMmSs) && flightInfo.contains(twoArrMmSs)){
                        // 选则航班需要用到的数据
                        String flightDetail = tr.parentNode().toString();
//                        log.info(request.getOrder().getOrderNo()+":航班信息："+flightInfo);
                        // 解析 HTML
                        document = Jsoup.parse(flightDetail);
                        // 选择包含 ECO 的 <td>
                        Elements ecoTd = document.select("td[data-familyid=Eco]");
                        // 如果找到，获取单选框的 value 值
                        if (!ecoTd.isEmpty()) {
                            Element ecoElement = ecoTd.first(); // 获取第一个匹配的 <td>
                            Elements radioButtons = ecoElement.select("input[type=radio]"); // 找到单选框
                            // 输出单选框的 value 值
                            for (Element radioButton : radioButtons) {
//                                log.info("ECO 单选框的 value 值: " + radioButton.val());
                                String val = radioButton.val();
                                return val;
                            }
                        } else {
//                            log.info("未找到 ECO 选项。");
                            return "ERROR_选择航班失败";
                        }
                    }
                }else {

                    if (flightInfo.contains(date) && flightInfo.contains(mmSs)) {
                        // 选则航班需要用到的数据
                        String flightDetail = tr.parentNode().toString();
//                        log.info(request.getOrder().getOrderNo()+":航班信息："+flightDetail);
                        // 解析 HTML
                        document = Jsoup.parse(flightDetail);
                        // 选择包含 ECO 的 <td>
                        Elements ecoTd = document.select("td[data-familyid=Eco]");
                        // 如果找到，获取单选框的 value 值
                        if (!ecoTd.isEmpty()) {
                            Element ecoElement = ecoTd.first(); // 获取第一个匹配的 <td>
                            Elements radioButtons = ecoElement.select("input[type=radio]"); // 找到单选框
                            // 输出单选框的 value 值
                            for (Element radioButton : radioButtons) {
//                                log.info("ECO 单选框的 value 值: " + radioButton.val());
                                String val = radioButton.val();
                                return val;
                            }
                        } else {
//                            log.info("未找到 ECO 选项。");
                            return "ERROR_选择航班失败";
                        }
                    }
                }
            }
        } else {
//            log.info("Flight not found");
            return "ERROR_选择航班失败";
        }
        return "ERROR_选择航班失败";
    }

    public String getSearchFlight(DefaultHttpClient client) {
        //                                 https://agents.vietjetair.com/ViewFlights.aspx?lang=en&st=sl&sesid=
        HttpGet httpGet = new HttpGet("https://agents.vietjetair.com/ViewFlights.aspx?lang=en&st=sl&sesid=");
        httpGet.addHeader("pragma", "no-cache");
        httpGet.addHeader("cache-control", "no-cache");
        httpGet.addHeader("upgrade-insecure-requests", "1");
        httpGet.addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36");
        httpGet.addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        httpGet.addHeader("sec-fetch-site", "same-origin");
        httpGet.addHeader("sec-fetch-mode", "navigate");
        httpGet.addHeader("sec-fetch-user", "?1");
        httpGet.addHeader("sec-fetch-dest", "document");
        httpGet.addHeader("sec-ch-ua", "\"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        httpGet.addHeader("sec-ch-ua-mobile", "?0");
        httpGet.addHeader("sec-ch-ua-platform", "\"Windows\"");
        httpGet.addHeader("referer", "https://agents.vietjetair.com/AgentOptions.aspx?lang=en&st=sl&sesid=");
        httpGet.addHeader("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");
        httpGet.addHeader("priority", "u=0, i");
        try {
            HttpResponse response = client.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == HttpURLConnection.HTTP_OK) {
                String info = readHtmlContentFromEntity(response.getEntity());
                getQueryParam(info);
                return info;
            } else {
                String info = readHtmlContentFromEntity(response.getEntity());
                logger.info("ERROR_getSearchFlight失败,response:"+info);
                return "ERROR_getSearchFlight失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR_getSearchFlight异常";
        }
    }



    public String searchFlight(DefaultHttpClient client, String cur, String sign, String dep ,String arr,String dep_time,String re_dep_time, int adult, int child, int infant) {
        String getSearchFlight = getSearchFlight(client);
        if (sign.contains("ERROR_")){
            return getSearchFlight;
        }

        HttpPost httpPost = new HttpPost("https://agents.vietjetair.com/ViewFlights.aspx?lang=en&st=sl&sesid=");
        httpPost.addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        httpPost.addHeader("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");
        httpPost.addHeader("cache-control", "no-cache");
        httpPost.addHeader("content-type", "application/x-www-form-urlencoded");
        httpPost.addHeader("origin", "https://agents.vietjetair.com");
        httpPost.addHeader("pragma", "no-cache");
        httpPost.addHeader("priority", "u=0, i");
        httpPost.addHeader("referer", "https://agents.vietjetair.com/ViewFlights.aspx?lang=en&st=sl&sesid=");
        httpPost.addHeader("sec-ch-ua", "\"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        httpPost.addHeader("sec-ch-ua-mobile", "?0");
        httpPost.addHeader("sec-ch-ua-platform", "\"Windows\"");
        httpPost.addHeader("sec-fetch-dest", "document");
        httpPost.addHeader("sec-fetch-mode", "navigate");
        httpPost.addHeader("sec-fetch-site", "same-origin");
        httpPost.addHeader("sec-fetch-user", "?1");
        httpPost.addHeader("upgrade-insecure-requests", "1");
        httpPost.addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36");

        try {


            String fromDate = yyyyMMddTOddmmyyyy(dep_time);
            String newFromDate = yyyyMMddTO2yyyy_MM_dd(dep_time);
            String dd = yyyyMMddTO2dd(dep_time);

            String reDate = "";
            String newReDate = "";
            String reDd = "";
            if (re_dep_time.isEmpty()) {
                reDate = yyyyMMddTOddmmyyyy(re_dep_time);
                newReDate = yyyyMMddTO2yyyy_MM_dd(re_dep_time);
                reDd = yyyyMMddTO2dd(re_dep_time);
            }else {
                reDate = fromDate;
                newReDate = newFromDate;
                reDd = dd;
            }

            List<NameValuePair> formData = new ArrayList<NameValuePair>();
            formData.add(new BasicNameValuePair("__VIEWSTATE", viewState));
            formData.add(new BasicNameValuePair("__VIEWSTATEGENERATOR", viewStateEgenerator));
            formData.add(new BasicNameValuePair("SesID", sesID));
            formData.add(new BasicNameValuePair("DebugID", debugID));
            formData.add(new BasicNameValuePair("button", "vfto"));
            formData.add(new BasicNameValuePair("dlstDepDate_Day", dd));
            formData.add(new BasicNameValuePair("dlstDepDate_Month", newFromDate));
            formData.add(new BasicNameValuePair("dlstRetDate_Day", reDd));
            formData.add(new BasicNameValuePair("dlstRetDate_Month", newReDate));
            formData.add(new BasicNameValuePair("lstDepDateRange", "0"));
            formData.add(new BasicNameValuePair("lstRetDateRange", "0"));

            formData.add(new BasicNameValuePair("SesID", sesID));
            formData.add(new BasicNameValuePair("DebugID", debugID));
            if (re_dep_time.isEmpty()){
                formData.add(new BasicNameValuePair("chkRoundTrip", "on"));
            }else {
                formData.add(new BasicNameValuePair("chkRoundTrip", ""));
            }

            formData.add(new BasicNameValuePair("lstOrigAP", dep));
            formData.add(new BasicNameValuePair("lstDestAP", arr));
            formData.add(new BasicNameValuePair("departure1", fromDate));
            formData.add(new BasicNameValuePair("departTime1", "0000"));
            formData.add(new BasicNameValuePair("departure2", reDate));
            formData.add(new BasicNameValuePair("departTime2", "0000"));

            formData.add(new BasicNameValuePair("lstLvlService", "1"));
            formData.add(new BasicNameValuePair("lstResCurrency", cur));
            formData.add(new BasicNameValuePair("txtNumAdults", String.valueOf(adult)));
            formData.add(new BasicNameValuePair("txtNumChildren", String.valueOf(child)));
            formData.add(new BasicNameValuePair("txtNumInfants", String.valueOf(infant)));
            formData.add(new BasicNameValuePair("txtPromoCode", ""));
            // USD : 12512ƒEAST AND WEST TRAVEL LIMITED
//            formData.add(new BasicNameValuePair("lstCompanyList", "13985ƒBEIJING LITTLE DRAGONFLY AIRLINE SERVICE CO., LTD"));
            formData.add(new BasicNameValuePair("lstCompanyList", sign));
            formData.add(new BasicNameValuePair("txtPONumber", ""));
            String format = URLEncodedUtils.format(formData, Charset.forName("UTF-8"));
            httpPost.setEntity(new StringEntity(format));
            HttpResponse response1 = client.execute(httpPost);
            String html = readHtmlContentFromEntity(response1.getEntity());
            if (response1.getStatusLine().getStatusCode() == 200 || response1.getStatusLine().getStatusCode() == 201) {
                getQueryParam(html);
                return html;
            } else {
                logger.info("ERROR_getSearchFlight失败,response:"+html);
                return "ERROR_获取航班失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR_获取航班失败";
        }
    }

    public String getSign(String info) {
        // 解析 HTML
        Document doc = Jsoup.parse(info);
        // 获取选中的 <option> 元素
        Element selectedOption = doc.select("select#lstCompanyList option[selected]").first();
        // 获取 value 属性
        if (selectedOption != null) {
            String value = selectedOption.attr("value");
            return value;
        }
        return "ERROR_获取账号秘钥失败,请重试或者联系技术";
    }

    public String agentOptions( DefaultHttpClient client,String cur) {
        String getAgentOptions = getAgentOptions(client);
        if (getAgentOptions.contains("ERROR_")){
            return getAgentOptions;
        }
        HttpPost httpPost = new HttpPost("https://agents.vietjetair.com/AgentOptions.aspx?lang=en&st=sl&sesid=");
        httpPost.addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        httpPost.addHeader("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");
        httpPost.addHeader("cache-control", "no-cache");
        httpPost.addHeader("content-type", "application/x-www-form-urlencoded");
        httpPost.addHeader("origin", "https://agents.vietjetair.com");
        httpPost.addHeader("pragma", "no-cache");
        httpPost.addHeader("priority", "u=0, i");
        httpPost.addHeader("referer", "https://agents.vietjetair.com/AgentOptions.aspx?lang=en&st=sl&sesid=");
        httpPost.addHeader("sec-ch-ua", "\"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        httpPost.addHeader("sec-ch-ua-mobile", "?0");
        httpPost.addHeader("sec-ch-ua-platform", "\"Windows\"");
        httpPost.addHeader("sec-fetch-dest", "document");
        httpPost.addHeader("sec-fetch-mode", "navigate");
        httpPost.addHeader("sec-fetch-site", "same-origin");
        httpPost.addHeader("sec-fetch-user", "?1");
        httpPost.addHeader("upgrade-insecure-requests", "1");
        httpPost.addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36");

        try {
            List<NameValuePair> formData = new ArrayList<NameValuePair>();
            formData.add(new BasicNameValuePair("__VIEWSTATE", viewState));
            formData.add(new BasicNameValuePair("__VIEWSTATEGENERATOR", viewStateEgenerator));
            formData.add(new BasicNameValuePair("SesID", sesID));
            formData.add(new BasicNameValuePair("DebugID", debugID));
            formData.add(new BasicNameValuePair("button", "bookflight"));
            formData.add(new BasicNameValuePair("selCurrency", cur));
            String format = URLEncodedUtils.format(formData, Charset.forName("UTF-8"));
            httpPost.setEntity(new StringEntity(format));
            HttpResponse response1 = client.execute(httpPost);
            String html = readHtmlContentFromEntity(response1.getEntity());
            if (response1.getStatusLine().getStatusCode() == 200 || response1.getStatusLine().getStatusCode() == 201) {
                return html;
            } else {
                logger.info("ERROR_agentOptions失败");
                return "ERROR_获取航班失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR_获取航班失败";
        }
    }

    public String getAgentOptions(DefaultHttpClient client) {
        HttpGet httpGet = new HttpGet("https://agents.vietjetair.com/AgentOptions.aspx?lang=en&st=sl&sesid=");
        httpGet.addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        httpGet.addHeader("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");
        httpGet.addHeader("cache-control", "no-cache");
        httpGet.addHeader("pragma", "no-cache");
        httpGet.addHeader("priority", "u=0, i");
        httpGet.addHeader("referer", "https://agents.vietjetair.com/sitelogin.aspx?lang=en");
        httpGet.addHeader("sec-ch-ua", "\"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        httpGet.addHeader("sec-ch-ua-mobile", "?0");
        httpGet.addHeader("sec-ch-ua-platform", "\"Windows\"");
        httpGet.addHeader("sec-fetch-dest", "document");
        httpGet.addHeader("sec-fetch-mode", "navigate");
        httpGet.addHeader("sec-fetch-site", "same-origin");
        httpGet.addHeader("sec-fetch-user", "?1");
        httpGet.addHeader("upgrade-insecure-requests", "1");
        httpGet.addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36");
        try {
            HttpResponse response = client.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == HttpURLConnection.HTTP_OK) {
                String info = readHtmlContentFromEntity(response.getEntity());
                getQueryParam(info);
                return "success";
            } else {
                String info = readHtmlContentFromEntity(response.getEntity());
                logger.info("ERROR_getAgentOptions失败");
                return "ERROR_getAgentOptions失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR_getAgentOptions异常";
        }
    }

    public String login(DefaultHttpClient client,String userName, String passWord) {

        HttpPost httpPost = new HttpPost("https://agents.vietjetair.com/sitelogin.aspx?lang=en");
        httpPost.addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        httpPost.addHeader("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");
        httpPost.addHeader("cache-control", "no-cache");
        httpPost.addHeader("content-type", "application/x-www-form-urlencoded");
        httpPost.addHeader("origin", "https://agents.vietjetair.com");
        httpPost.addHeader("pragma", "no-cache");
        httpPost.addHeader("priority", "u=0, i");
        httpPost.addHeader("referer", "https://agents.vietjetair.com/sitelogin.aspx?lang=en");
        httpPost.addHeader("sec-ch-ua", "\"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        httpPost.addHeader("sec-ch-ua-mobile", "?0");
        httpPost.addHeader("sec-ch-ua-platform", "\"Windows\"");
        httpPost.addHeader("sec-fetch-dest", "document");
        httpPost.addHeader("sec-fetch-mode", "navigate");
        httpPost.addHeader("sec-fetch-site", "same-origin");
        httpPost.addHeader("sec-fetch-user", "?1");
        httpPost.addHeader("upgrade-insecure-requests", "1");
        httpPost.addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36");
        try {
            List<NameValuePair> formData = new ArrayList<NameValuePair>();
            formData.add(new BasicNameValuePair("__VIEWSTATE", viewState));
            formData.add(new BasicNameValuePair("__VIEWSTATEGENERATOR", viewStateEgenerator));
            formData.add(new BasicNameValuePair("SesID", sesID));
            formData.add(new BasicNameValuePair("DebugID", debugID));
            formData.add(new BasicNameValuePair("txtAgentID", userName));
            formData.add(new BasicNameValuePair("txtAgentPswd", passWord));
            String format = URLEncodedUtils.format(formData, Charset.forName("UTF-8"));
            httpPost.setEntity(new StringEntity(format));
            HttpResponse response1 = client.execute(httpPost);
            String html = readHtmlContentFromEntity(response1.getEntity());
            if (response1.getStatusLine().getStatusCode() == 200 || response1.getStatusLine().getStatusCode() == 201) {
                getQueryParam(html);
                return html;
            } else {
                logger.info("ERROR_登录失败");
                return "ERROR_登录失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR_登录失败";
        }
    }



    public String getLoginParam(DefaultHttpClient client) {
        HttpGet httpGet = new HttpGet("https://agents.vietjetair.com/sitelogin.aspx?lang=en");
        httpGet.addHeader("sec-ch-ua", "\"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"");
        httpGet.addHeader("sec-ch-ua-mobile", "?0");
        httpGet.addHeader("sec-ch-ua-platform", "\"Windows\"");
        httpGet.addHeader("upgrade-insecure-requests", "1");
        httpGet.addHeader("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36");
        httpGet.addHeader("accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        httpGet.addHeader("sec-fetch-site", "same-site");
        httpGet.addHeader("sec-fetch-mode", "navigate");
        httpGet.addHeader("sec-fetch-user", "?1");
        httpGet.addHeader("sec-fetch-dest", "document");
        httpGet.addHeader("referer", "https://www.vietjetair.com/");
        httpGet.addHeader("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");
        httpGet.addHeader("priority", "u=0, i");
        try {
            HttpResponse response = client.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == HttpURLConnection.HTTP_OK) {
                String info = readHtmlContentFromEntity(response.getEntity());
                getQueryParam(info);
                return info;
            } else {
                String info = readHtmlContentFromEntity(response.getEntity());
                logger.info("ERROR_首页失败"+info);
                return "ERROR_首页失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR_首页异常";
        }
    }

    private void getQueryParam(String info) {
        try {
            String htmlContent = info;
            String viewStatePattern = "__VIEWSTATE\" value=\"(.*?)\"";
            String viewStateGenPattern = "__VIEWSTATEGENERATOR\" value=\"(.*?)\"";
            String sesIdPattern = "name='SesID' value='(.*?)'";
            String debugIdPattern = "name='DebugID' value='(.*?)'";
            viewState = extractValue(htmlContent, viewStatePattern);
            viewStateEgenerator = extractValue(htmlContent, viewStateGenPattern);
            sesID = extractValue(htmlContent, sesIdPattern);
            debugID = extractValue(htmlContent, debugIdPattern);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String extractValue(String html, String pattern) {
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(html);
        if (m.find()) {
            return m.group(1);
        }
        return null;
    }

    public synchronized void proxy_ip(DefaultHttpClient client) {

//        String proxyHost = "gw.ntnt.io";
//        Integer proxyPort = 5959;
//        String proxyUser = "hourse-res-ru";
//        String proxyPass = "9zhu4rntOuaLD1w";

//
//        String proxyHost = "t101.juliangip.cc";
//        Integer proxyPort = 11890;
//        String proxyUser = "18201068924";
//        String proxyPass = "hhXwlqfn";

        String proxyHost = proxy_host;
        Integer proxyPort = proxy_port;
        String proxyUser = proxy_user;
        String proxyPass = proxy_pass;

//        String proxy_url = "http://" + proxyUser + ":" + proxyPass + "@" + proxyHost + ":" + proxyPort;
//        logger.info("{}本次client所用代理:" + proxy_url);

        HttpHost proxy = new HttpHost(proxyHost, proxyPort, "http");
        client.getParams().setParameter(ConnRoutePNames.DEFAULT_PROXY, proxy);
        AuthScope auth = new AuthScope(proxyHost, proxyPort);
        Credentials credentials = new org.apache.http.auth.NTCredentials(proxyUser, proxyPass, "", "");
        client.getCredentialsProvider().setCredentials(auth, credentials);
    }


}
