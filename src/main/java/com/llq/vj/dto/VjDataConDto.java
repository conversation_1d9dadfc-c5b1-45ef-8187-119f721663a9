package com.llq.vj.dto;


import com.llq.vj.config.VjApiConfig;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 2025/07/08
 **/
public class VjDataConDto extends VjTokenDto {


    /**
     * 构建查询条件
     * @return
     * @param tripType
     * @param depAirport
     * @param arrAirport
     * @param departureDate
     * @param currency
     * @param seq
     */
    public static VjDataConDto buildCon(String tripType, String depAirport, String arrAirport, String departureDate,
                                        String currency, int seq){
        VjDataConDto conDto = new VjDataConDto();

        VjDataParamDto VjDataParamDto = new VjDataParamDto();
        VjDataParamDto.setTripType(tripType);
        VjDataParamDto.setCurrency(currency);

        VjDataParamRouteDto routeDto = new VjDataParamRouteDto();
        routeDto.setDepartureDate(departureDate);
        routeDto.setOrigin(depAirport);
        routeDto.setDestination(arrAirport);

        VjDataParamDto.setRoutes(Arrays.asList(routeDto));

        VjTokenDataDto data = new VjTokenDataDto();
        data.setSearchParams(VjDataParamDto);
        data.setPool(VjApiConfig.getPoolIdx(seq));
        conDto.setData(data);

        return conDto;
    }

}
