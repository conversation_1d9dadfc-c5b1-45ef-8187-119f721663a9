package com.llq.vj.dto;

import cn.hutool.core.util.RandomUtil;
import lombok.Getter;

import java.util.Random;

/**
 * <AUTHOR>
 * @version 2025/08/11
 **/

@Getter
public class VjProxyConfigDTO {

    private String proxyUser;
    private String proxyPass;
    private String proxyHost;
    private String proxyPort;


    public VjProxyConfigDTO() {
    }

    public VjProxyConfigDTO(String proxyUser, String proxyPass, String proxyHost, String proxyPort) {
        this.proxyUser = proxyUser;
        this.proxyPass = proxyPass;
        this.proxyHost = proxyHost;
        this.proxyPort = proxyPort;
    }

    static VjProxyConfigDTO[] proxy = new VjProxyConfigDTO[]{
            new VjProxyConfigDTO(
                    "user-sub.15946004909-country-us-session-a8qu8b",
                    "s4836229",
                    "proxy.haiwaiip.com",
                    "1461")
    };

    /**
     * 随机获取
     * @return
     */
    public static VjProxyConfigDTO getProxy(){
        /*int i = new Random().nextInt(10);
        return proxy[i%2];*/
        return new VjProxyConfigDTO(
                "user-sub.mrWGOODAIR3579-country-ru-session-" + RandomUtil.randomString(6),
                "GOODAIR1589",
                "proxy.haiwaiip.com",
                "1461");
    }

    @Override
    public String toString() {
        return "S7ProxyConfigDTO{" +
               // "proxyUser='" + proxyUser + '\'' +
                //", proxyPass='" + proxyPass + '\'' +
                ", proxyHost='" + proxyHost + '\'' +
                ", proxyPort='" + proxyPort + '\'' +
                '}';
    }
}
