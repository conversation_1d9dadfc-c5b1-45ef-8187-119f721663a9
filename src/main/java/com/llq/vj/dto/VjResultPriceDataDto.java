package com.llq.vj.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/10
 **/
@Data
public class VjResultPriceDataDto {


    /**
     * 原始币种三字码，爬虫采用的价格币种
     */
    private String originalCurrency;

    /**
     * 报价转换时的汇率值
     */
    private BigDecimal exchangeRate;


    /**
     * 库存座位数，实时库存
     */
    private int seatCount;


    /**
     * 基准底价
     */
    private BigDecimal basePrice;

    /**
     * 税费
     */
    private BigDecimal taxFeeAount;

    /**
     * 行李额件数，单位PC，枚举值如下 0：表示无免费托运行李， -1：表示行李采用计重制， n（n>0）：表示行李采用计件制，每人可携带n件行李
     */
    private Integer baggageQuantity;

    /**
     * 行李额重量，单位KG，跟xingLiPiece配合使用，无免费行李时，填-1
     */
    private Integer baggageWeight;



    /**
     * 客票全部未使用时不可改期，1不可改期，0可以改期，如果不可改期，没必要传改期费了
     */
    private Integer noChange=1;

    /**
     * 客票全部未使用时起飞前几小时可改期，默认0
     */
    private Integer changeHour=0;

    /**
     * 客票全部未使用时的改期费
     */
    private BigDecimal changeFee;

    /**
     * 客票全部未使用时不可推票，1不可退票，0可以退票，如果不可退票，没必要传退票费了
     */
    private Integer noRefund=1;

    /**
     * 客票全部未使用时起飞前几小时可退票，默认0
     */
    private Integer refundHour=0;

    /**
     * 客票全部未使用时的退票费
     */
    private BigDecimal refundFee;



    /**
     * 客票返程未使用时不可改期，1不可改期，0可以改期，如果不可改期，没必要传改期费了
     * 往返政策才用到
     */
    private Integer noChange2=1;

    /**
     * 客票返程未使用时起飞前几小时可改期，默认0
     * 往返政策才用到
     */
    private Integer changeHour2=0;

    /**
     * 客票返程未使用时的改期费
     * 往返政策才用到
     */
    private BigDecimal changeFee2;

    /**
     * 客票返程未使用时不可推票，1不可退票，0可以退票，如果不可退票，没必要传退票费了
     * 往返政策才用到
     */
    private Integer noRefund2=1;

    /**
     * 客票返程未使用时起飞前几小时可退票，默认0
     * 往返政策才用到
     */
    private Integer refundHour2=0;

    /**
     * 客票返程未使用时的退票费
     * 往返政策才用到
     */
    private BigDecimal refundFee2;


    /**
     * 封装政策的去程航段信息，航段必须严格按照起飞时间先后顺序放入list中
     */
    private List<VjResultPriceDataSegmentDto> priceDataSegmentDtoList_go;

    /**
     * 封装政策的回程航段信息，航段必须严格按照起飞时间先后顺序放入list中
     * 构造往返政策时必传，单程不能传值，需要根据它来判断是否为往返政策
     */
    private List<VjResultPriceDataSegmentDto> priceDataSegmentDtoList_back;


}
