package com.llq.vj.dto;


import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/10
 **/

public class VjResultDto {


    private boolean success;

    private List<VjResultDataDto> data;

    private String msg;



    public VjResultDto(boolean success, List<VjResultDataDto> data, String msg) {
        this.success = success;
        this.data = data;
        this.msg = msg;
    }

    public static VjResultDto buildOk(){
        return new VjResultDto(true,new ArrayList<>(),"success");
    }

    public static VjResultDto buildFail(String msg){
        return new VjResultDto(false,null,msg);
    }


    public List<VjResultDataDto> getData() {
        return data;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getMsg() {
        return msg;
    }

    @Override
    public String toString() {
        return "S7ResultDto{" +
                "success=" + success +
                ", data=" + data +
                ", msg='" + msg + '\'' +
                '}';
    }
}
