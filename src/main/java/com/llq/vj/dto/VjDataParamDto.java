package com.llq.vj.dto;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/07
 **/
public class VjDataParamDto {

    /**
     *
     * 查询币种
     */
    private String currency;


    /**
     * 直飞
     */
    private Boolean directOnly = false;
    private Boolean ownAirlineOnly = false;


    /**
     * 乘客信息
     */
    private VjDataParamPassengerDto passengersAmount = new VjDataParamPassengerDto();

    /**
     * 促销码
     */
    private String promoCode = "";


    /**
     *
     */
    private Boolean redemption = false;

    /**
     * 行程
     */
    private List<VjDataParamRouteDto> routes;



    private String searchType = "EXACT";


    private String[] subsidizedPassengerTypes = {};

    /**
     * 行程类型
     * 单程、往返、多程
     */
    private String tripType;


    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Boolean getDirectOnly() {
        return directOnly;
    }

    public void setDirectOnly(Boolean directOnly) {
        this.directOnly = directOnly;
    }

    public Boolean getOwnAirlineOnly() {
        return ownAirlineOnly;
    }

    public void setOwnAirlineOnly(Boolean ownAirlineOnly) {
        this.ownAirlineOnly = ownAirlineOnly;
    }

    public VjDataParamPassengerDto getPassengersAmount() {
        return passengersAmount;
    }

    public void setPassengersAmount(VjDataParamPassengerDto passengersAmount) {
        this.passengersAmount = passengersAmount;
    }

    public String getPromoCode() {
        return promoCode;
    }

    public void setPromoCode(String promoCode) {
        this.promoCode = promoCode;
    }

    public Boolean getRedemption() {
        return redemption;
    }

    public void setRedemption(Boolean redemption) {
        this.redemption = redemption;
    }

    public List<VjDataParamRouteDto> getRoutes() {
        return routes;
    }

    public void setRoutes(List<VjDataParamRouteDto> routes) {
        this.routes = routes;
    }

    public String getSearchType() {
        return searchType;
    }

    public void setSearchType(String searchType) {
        this.searchType = searchType;
    }

    public String[] getSubsidizedPassengerTypes() {
        return subsidizedPassengerTypes;
    }

    public void setSubsidizedPassengerTypes(String[] subsidizedPassengerTypes) {
        this.subsidizedPassengerTypes = subsidizedPassengerTypes;
    }

    public String getTripType() {
        return tripType;
    }

    public void setTripType(String tripType) {
        this.tripType = tripType;
    }

    @Override
    public String toString() {
        return "searchParams{" +
                "currency='" + currency + '\'' +
                ", directOnly=" + directOnly +
                ", ownAirlineOnly=" + ownAirlineOnly +
                ", passengersAmount=" + passengersAmount +
                ", promoCode='" + promoCode + '\'' +
                ", redemption=" + redemption +
                ", routes=" + routes +
                ", searchType='" + searchType + '\'' +
                ", subsidizedPassengerTypes=" + Arrays.toString(subsidizedPassengerTypes) +
                ", tripType='" + tripType + '\'' +
                '}';
    }
}
