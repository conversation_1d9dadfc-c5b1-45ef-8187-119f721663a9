package com.llq.vj.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @version 2025/07/10
 **/
public class LccDataUtil {


    /**
     * 去掉前导0字符
     * @param inData
     * @return
     */
    public static String trimLeftZero(String inData){
        return  inData.replaceFirst("^0+", "");
    }


    /**
     *
     * @param val
     * @return
     */
    public static Double transIntAmount(BigDecimal val){
        if ( null != val ){
            val = val.setScale(0, RoundingMode.UP);
            return val.doubleValue();
        } else {
            return null;
        }
    }

    /**
     *
     * @param val
     * @return
     */
    public static Double transIntAmount(BigDecimal val, int scale){
        if ( null != val ){
            val = val.setScale(scale, RoundingMode.UP);
            return val.doubleValue();
        } else {
            return null;
        }
    }

    /**
     *
     * @param val
     * @return
     */
    public static BigDecimal scale(BigDecimal val, int scale){
        if ( null != val ){
            return val.setScale(scale, RoundingMode.HALF_UP);
        } else {
            return null;
        }
    }


    public static void main(String[] args) {
        System.out.println(trimLeftZero("00050724"));
        System.out.println(transIntAmount(new BigDecimal(100.15)));
        double costSeconds = 12 /1000.0d;
        System.out.println(String.format("%02d",costSeconds));
    }


}
