package com.llq.vj.util;

import com.google.common.base.Strings;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2025/07/10
 **/
public class LccDateUtil {


    public static final String DATE_PATTERN_YMDHM = "yyyyMMddHHmm";


    public static final String DATE_PATTERN_YMD = "yyyyMMdd";

    public static final String DATE_PATTERN_Y_M_D = "yyyy-MM-dd";

    public static final String DATE_PATTERN_Y_M_D_UNDER = "yyyy_MM_dd";

    /**
     * 日期格式转换
     *
     * @param inData
     * @param pattern
     * @param retPattern
     * @return
     */
    public static String tranData(String inData, String pattern, String retPattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            Date date = sdf.parse(inData);
            return new SimpleDateFormat(retPattern).format(date);
        } catch (Exception e) {
            e.printStackTrace();
            return inData;
        }
    }


    /**
     * 日期格式转换
     * <p>
     * 输入 "2025-08-29T06:05:00"
     * 输出“YYYY-MM-DD”
     *
     * @param inData
     * @return
     */
    public static String tranDate(String inData) {
        try {
            return inData.substring(0, 10);
        } catch (Exception e) {
            e.printStackTrace();
            return inData;
        }
    }


    /**
     * 日期格式转换
     * <p>
     * 输入"2025-08-22T06:05:00",
     * 输出“YYYYMMDDHHMM”
     *
     * @param inData
     * @return
     */
    public static String tranTime(String inData) {
        try {
            String str = inData.replaceAll("-", "")
                    .replace("T", "")
                    .replaceAll(" ","")
                    .replaceAll(":", "");
            return str.substring(0, 12);
        } catch (Exception e) {
            e.printStackTrace();
            return inData;
        }
    }


    /**
     * 解析日期
     *
     * @param date
     * @return
     */
    public static Date parseDate(String date) {
        try {
            return new SimpleDateFormat(DATE_PATTERN_Y_M_D).parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
            throw new RuntimeException("日期格式解析异常：" + e.getMessage());
        }
    }

    /**
     * 根据出生日期计算年龄
     * @param birthDateStr 出生日期
     * @return 计算出的年龄
     */
    public static int calculateAge(String birthDateStr) {
        if (Strings.isNullOrEmpty(birthDateStr)) {
            throw new IllegalArgumentException("出生日期不能为null");
        }
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_PATTERN_Y_M_D);
        LocalDate birthDate = LocalDate.parse(birthDateStr, formatter);
        Period period = Period.between(birthDate, currentDate);
        return period.getYears();
    }


    /**
     * DATE_PATTERN_Y_M_D
     * <p>
     * 根据解析日期
     *
     * @param dateStr
     * @return
     * @throws Exception
     */
    public static XMLGregorianCalendar stringToXMLGregorian(String dateStr) {
        try {
            DatatypeFactory factory = DatatypeFactory.newInstance();
            return factory.newXMLGregorianCalendar(dateStr);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("日期解析错误:" + dateStr);
        }
    }

    /**
     *  202508070535
     *  返回2025-08-07
     * @param dateStr
     * @return
     */
    public static String getDate(String dateStr) {
        try {
           Date date = new SimpleDateFormat(DATE_PATTERN_YMDHM).parse(dateStr);
           return new SimpleDateFormat(DATE_PATTERN_Y_M_D).format(date);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("日期解析错误:" + dateStr);
        }
    }

    /**
     *  202508070535
     *  返回2025-08-07
     * @param dateStr
     * @return
     */
    public static String getTime(String dateStr) {
        try {
            Date date = new SimpleDateFormat(DATE_PATTERN_YMDHM).parse(dateStr);
            return new SimpleDateFormat("HH:mm").format(date);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("日期解析错误:" + dateStr);
        }
    }


    /**
     * 获取yyyy-MM-dd
     * @param xcal
     * @return
     */
    public static String xmlGregorianToString(XMLGregorianCalendar xcal){
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN_Y_M_D);
        return sdf.format(xcal.toGregorianCalendar().getTime());
    }


    public static void main(String[] args) {
        //202508220605
        System.out.println(tranTime("2025-08-22T06:05:00"));
        System.out.println(tranDate("2025-08-22T06:05:00"));
    }


}
