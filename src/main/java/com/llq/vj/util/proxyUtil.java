package com.llq.vj.util;

import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;

import static com.llq.vj.http.VjHttpUtil.readHtmlContentFromEntity;

public class proxyUtil {

    public static String getProxyIP(DefaultHttpClient client) {
//        String url = "http://v2.api.juliangip.com/company/postpay/getips?auth_type=2&auto_white=1&num=1&pt=1&result_type=text&split=1&trade_no=6857272933332334&sign=9b21c800aa5f22d8648846cdb76738e3";
        String url = "http://luluqi.user.xiecaiyun.com/api/proxies?action=getText&key=NPEEA975B7&count=100&word=&rand=false&norepeat=false&detail=false&ltime=&idshow=false";
        HttpGet httpGet = new HttpGet(url);
        try {
            HttpResponse httpResponse = client.execute(httpGet);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            String htmlContent = readHtmlContentFromEntity(httpResponse.getEntity());
            if (statusCode != 200) {
                return "ERROR_获取ip失败";
            }
            return htmlContent;
        } catch (Exception e) {
            ////e.printStackTrace();
            return "ERROR_获取ip异常";
        }
    }
}
