package com.llq.vj.util;

import com.llq.vj.service.Lcc_HuilvService;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 汇率助手类
 * 解决在非Spring管理的类中获取汇率的问题
 * 
 * <AUTHOR>
 * @version 2025-10-28
 */
@Component
public class ExchangeRateHelper {
    
    @Resource
    private Lcc_HuilvService lccHuilvService;
    
    private static ExchangeRateHelper instance;
    
    @PostConstruct
    public void init() {
        instance = this;
    }
    
    /**
     * 静态方法获取汇率
     * @param oriCurrency 原币种
     * @return 汇率
     */
    public static BigDecimal getExchangeRate(String oriCurrency) {
        if (instance == null || instance.lccHuilvService == null) {
            System.err.println("汇率服务未初始化，返回默认汇率1.0");
            return BigDecimal.ONE;
        }
        
        try {
            return instance.lccHuilvService.getExchangeRate(oriCurrency, Lcc_HuilvService.CNY);
        } catch (Exception e) {
            System.err.println("获取汇率失败，币种: " + oriCurrency + "，错误: " + e.getMessage());
            e.printStackTrace();
            return BigDecimal.ONE;
        }
    }
    
    /**
     * 静态方法获取汇率（指定目标币种）
     * @param oriCurrency 原币种
     * @param destCurrency 目标币种
     * @return 汇率
     */
    public static BigDecimal getExchangeRate(String oriCurrency, String destCurrency) {
        if (instance == null || instance.lccHuilvService == null) {
            System.err.println("汇率服务未初始化，返回默认汇率1.0");
            return BigDecimal.ONE;
        }
        
        try {
            return instance.lccHuilvService.getExchangeRate(oriCurrency, destCurrency);
        } catch (Exception e) {
            System.err.println("获取汇率失败，原币种: " + oriCurrency + "，目标币种: " + destCurrency + "，错误: " + e.getMessage());
            e.printStackTrace();
            return BigDecimal.ONE;
        }
    }
}
