package com.llq.vj.util;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 航班选择助手类
 * 用于从VietJet HTML页面中智能选择航班参数
 * 
 * <AUTHOR>
 * @version 2025-10-28
 */
public class FlightSelectionHelper {
    
    /**
     * 根据指定条件选择最佳航班参数
     * 
     * @param htmlFilePath HTML文件路径
     * @param criteria 选择条件
     * @return 选择的航班参数，如 "4,A1_ECO,O"
     */
    public static String selectBestFlight(String htmlFilePath, SelectionCriteria criteria) {
        try {
            File htmlFile = new File(htmlFilePath);
            Document doc = Jsoup.parse(htmlFile, "UTF-8");
            
            // 获取所有航班选项
            List<FlightOption> options = parseAllFlightOptions(doc);
            
            // 根据条件筛选
            List<FlightOption> filtered = options.stream()
                    .filter(option -> matchesCriteria(option, criteria))
                    .collect(Collectors.toList());
            
            if (filtered.isEmpty()) {
                System.out.println("没有找到符合条件的航班选项");
                return null;
            }
            
            // 根据优先级排序选择最佳选项
            FlightOption selected = selectBestOption(filtered, criteria);
            
            System.out.println("选择的航班: " + selected);
            return selected.getValue();
            
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 解析所有航班选项
     */
    private static List<FlightOption> parseAllFlightOptions(Document doc) {
        List<FlightOption> options = new ArrayList<>();
        
        // 查找所有radio按钮
        Elements radioButtons = doc.select("input[type=radio]");
        Pattern pattern = Pattern.compile("^\\d+,[A-Z0-9]+_[A-Z]+,[A-Z]$");
        
        for (Element radio : radioButtons) {
            String value = radio.attr("value");
            if (value != null && pattern.matcher(value).matches()) {
                FlightOption option = parseFlightOption(radio, value);
                if (option != null) {
                    options.add(option);
                }
            }
        }
        
        return options;
    }
    
    /**
     * 解析单个航班选项
     */
    private static FlightOption parseFlightOption(Element radio, String value) {
        Element parentTd = radio.closest("td");
        if (parentTd == null) return null;
        
        FlightOption option = new FlightOption();
        option.setValue(value);
        option.setName(radio.attr("name"));
        option.setOnClick(radio.attr("onclick"));
        
        // 解析value中的信息
        String[] parts = value.split(",");
        if (parts.length >= 3) {
            option.setFlightIndex(Integer.parseInt(parts[0]));
            option.setFareCode(parts[1]);
            option.setCabinClass(parts[2]);
        }
        
        // 解析价格和其他隐藏字段
        parseHiddenFields(parentTd, option);
        
        // 获取舱位类型
        option.setFamilyId(parentTd.attr("data-familyid"));
        
        return option;
    }
    
    /**
     * 解析隐藏字段信息
     */
    private static void parseHiddenFields(Element parentTd, FlightOption option) {
        Elements hiddenInputs = parentTd.select("input[type=hidden]");
        
        for (Element hidden : hiddenInputs) {
            String id = hidden.attr("id");
            String value = hidden.attr("value");
            
            switch (id) {
                case "fare":
                    option.setFare(value);
                    try {
                        option.setPrice(Double.parseDouble(value.replaceAll("[^\\d.]", "")));
                    } catch (NumberFormatException e) {
                        option.setPrice(Double.MAX_VALUE);
                    }
                    break;
                case "total_fare":
                    option.setTotalFare(value);
                    break;
                case "depTime":
                    option.setDepartureTime(value);
                    break;
                case "charges":
                    option.setCharges(value);
                    break;
                case "total_complete_charges":
                    option.setTotalCompleteCharges(value);
                    break;
            }
        }
    }
    
    /**
     * 检查航班选项是否符合条件
     */
    private static boolean matchesCriteria(FlightOption option, SelectionCriteria criteria) {
        // 检查票价代码
        if (criteria.getFareCodePrefix() != null && 
            !option.getFareCode().startsWith(criteria.getFareCodePrefix())) {
            return false;
        }
        
        // 检查舱位类型
        if (criteria.getCabinClass() != null && 
            !option.getFareCode().contains(criteria.getCabinClass())) {
            return false;
        }
        
        // 检查最大价格
        if (criteria.getMaxPrice() != null && option.getPrice() > criteria.getMaxPrice()) {
            return false;
        }
        
        // 检查最小价格
        if (criteria.getMinPrice() != null && option.getPrice() < criteria.getMinPrice()) {
            return false;
        }
        
        // 检查航班方向（去程/返程）
        if (criteria.getFlightDirection() != null) {
            boolean isDeparture = "gridTravelOptDep".equals(option.getName());
            boolean isReturn = "gridTravelOptRet".equals(option.getName());
            
            if (criteria.getFlightDirection() == FlightDirection.DEPARTURE && !isDeparture) {
                return false;
            }
            if (criteria.getFlightDirection() == FlightDirection.RETURN && !isReturn) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 选择最佳选项
     */
    private static FlightOption selectBestOption(List<FlightOption> options, SelectionCriteria criteria) {
        return options.stream()
                .min((a, b) -> compareOptions(a, b, criteria))
                .orElse(null);
    }
    
    /**
     * 比较两个航班选项
     */
    private static int compareOptions(FlightOption a, FlightOption b, SelectionCriteria criteria) {
        // 优先级1: 价格（如果指定价格优先）
        if (criteria.isPriceFirst()) {
            int priceCompare = Double.compare(a.getPrice(), b.getPrice());
            if (priceCompare != 0) return priceCompare;
        }
        
        // 优先级2: 票价代码优先级
        int fareCodeCompare = compareFareCode(a.getFareCode(), b.getFareCode());
        if (fareCodeCompare != 0) return fareCodeCompare;
        
        // 优先级3: 航班索引（较小的优先）
        return Integer.compare(a.getFlightIndex(), b.getFlightIndex());
    }
    
    /**
     * 比较票价代码优先级
     */
    private static int compareFareCode(String fareCodeA, String fareCodeB) {
        // 定义票价代码优先级 (E < A < Z < W < J)
        Map<String, Integer> priority = new HashMap<>();
        priority.put("E", 1);  // 最便宜
        priority.put("A", 2);
        priority.put("Z", 3);
        priority.put("W", 4);
        priority.put("J", 5);  // 最贵
        
        String prefixA = fareCodeA.substring(0, 1);
        String prefixB = fareCodeB.substring(0, 1);
        
        int priorityA = priority.getOrDefault(prefixA, 999);
        int priorityB = priority.getOrDefault(prefixB, 999);
        
        return Integer.compare(priorityA, priorityB);
    }
    
    /**
     * 航班方向枚举
     */
    public enum FlightDirection {
        DEPARTURE,  // 去程
        RETURN      // 返程
    }
    
    /**
     * 选择条件类
     */
    public static class SelectionCriteria {
        private String fareCodePrefix;      // 票价代码前缀，如 "A1"
        private String cabinClass;          // 舱位类型，如 "ECO"
        private Double maxPrice;            // 最大价格
        private Double minPrice;            // 最小价格
        private FlightDirection flightDirection; // 航班方向
        private boolean priceFirst = true;  // 是否价格优先
        
        // Getters and Setters
        public String getFareCodePrefix() { return fareCodePrefix; }
        public void setFareCodePrefix(String fareCodePrefix) { this.fareCodePrefix = fareCodePrefix; }
        
        public String getCabinClass() { return cabinClass; }
        public void setCabinClass(String cabinClass) { this.cabinClass = cabinClass; }
        
        public Double getMaxPrice() { return maxPrice; }
        public void setMaxPrice(Double maxPrice) { this.maxPrice = maxPrice; }
        
        public Double getMinPrice() { return minPrice; }
        public void setMinPrice(Double minPrice) { this.minPrice = minPrice; }
        
        public FlightDirection getFlightDirection() { return flightDirection; }
        public void setFlightDirection(FlightDirection flightDirection) { this.flightDirection = flightDirection; }
        
        public boolean isPriceFirst() { return priceFirst; }
        public void setPriceFirst(boolean priceFirst) { this.priceFirst = priceFirst; }
        
        // 便捷构造方法
        public static SelectionCriteria forEconomyClass() {
            SelectionCriteria criteria = new SelectionCriteria();
            criteria.setCabinClass("ECO");
            return criteria;
        }
        
        public static SelectionCriteria forFareCode(String fareCodePrefix) {
            SelectionCriteria criteria = new SelectionCriteria();
            criteria.setFareCodePrefix(fareCodePrefix);
            return criteria;
        }
        
        public static SelectionCriteria forPriceRange(double minPrice, double maxPrice) {
            SelectionCriteria criteria = new SelectionCriteria();
            criteria.setMinPrice(minPrice);
            criteria.setMaxPrice(maxPrice);
            return criteria;
        }
    }
    
    /**
     * 航班选项类
     */
    public static class FlightOption {
        private String value;
        private String name;
        private String onClick;
        private int flightIndex;
        private String fareCode;
        private String cabinClass;
        private String familyId;
        private String fare;
        private String totalFare;
        private String departureTime;
        private String charges;
        private String totalCompleteCharges;
        private double price;
        
        // Getters and Setters
        public String getValue() { return value; }
        public void setValue(String value) { this.value = value; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getOnClick() { return onClick; }
        public void setOnClick(String onClick) { this.onClick = onClick; }
        
        public int getFlightIndex() { return flightIndex; }
        public void setFlightIndex(int flightIndex) { this.flightIndex = flightIndex; }
        
        public String getFareCode() { return fareCode; }
        public void setFareCode(String fareCode) { this.fareCode = fareCode; }
        
        public String getCabinClass() { return cabinClass; }
        public void setCabinClass(String cabinClass) { this.cabinClass = cabinClass; }
        
        public String getFamilyId() { return familyId; }
        public void setFamilyId(String familyId) { this.familyId = familyId; }
        
        public String getFare() { return fare; }
        public void setFare(String fare) { this.fare = fare; }
        
        public String getTotalFare() { return totalFare; }
        public void setTotalFare(String totalFare) { this.totalFare = totalFare; }
        
        public String getDepartureTime() { return departureTime; }
        public void setDepartureTime(String departureTime) { this.departureTime = departureTime; }
        
        public String getCharges() { return charges; }
        public void setCharges(String charges) { this.charges = charges; }
        
        public String getTotalCompleteCharges() { return totalCompleteCharges; }
        public void setTotalCompleteCharges(String totalCompleteCharges) { this.totalCompleteCharges = totalCompleteCharges; }
        
        public double getPrice() { return price; }
        public void setPrice(double price) { this.price = price; }
        
        @Override
        public String toString() {
            return "FlightOption{" +
                    "value='" + value + '\'' +
                    ", fareCode='" + fareCode + '\'' +
                    ", price=" + price +
                    ", departureTime='" + departureTime + '\'' +
                    ", familyId='" + familyId + '\'' +
                    '}';
        }
    }
}
