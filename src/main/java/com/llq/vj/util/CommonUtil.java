package com.llq.vj.util;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;

public class CommonUtil {

    public static String yyyyMMddTOddmmyyyy(String date) {
        if(StringUtils.isBlank(date)){
            return date;
        }
        String yyyy = date.substring(0, 4);
        String mm = date.substring(4, 6);
        String dd = date.substring(6, 8);
        return dd + "/" + mm + "/" + yyyy;
    }

    public static String yyyyMMddTO2yyyy_MM_dd(String date) {
        if (StringUtils.isBlank(date)) {
            return date;
        }
        String yyyy = date.substring(0, 4);
        String mm = date.substring(4, 6);
        return yyyy + "/" + mm;
    }

    public static String yyyyMMddTO2dd(String date) {
        if (StringUtils.isBlank(date)) {
            return date;
        }
        String dd = date.substring(6, 8);
        return dd;
    }

    public static String getMmSs(String date) {
        if (StringUtils.isBlank(date)) {
            return date;
        }
        String mm = date.substring(8, 10);
        String ss = date.substring(10, 12);
        return mm+":"+ss;
    }
}
