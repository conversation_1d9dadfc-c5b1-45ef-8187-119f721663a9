package com.llq.vj.util;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;


/**
 * 
 * xml转换工具
 * 
 * <AUTHOR>
 * 
 */
public class XmlUtil {

	/**
	 * 
	 * obj转xml
	 * 
	 * @param obj
	 * @return
	 */
	public static String object2Xml(Object obj) {
		try {
			JAXBContext context = JAXBContext.newInstance(obj.getClass());
			Marshaller marshaller = context.createMarshaller();
			marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true); // 格式化输出
			StringWriter writer = new StringWriter();
			marshaller.marshal(obj, writer);
			return writer.toString();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("对象转XML失败");
		}
	}

	/**
	 * 
	 * obj转xml
	 * 
	 * @param xml
	 * @param clazz
	 * @return
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T xml2Object(String xml, Class<T> clazz) {
		try {
			JAXBContext context = JAXBContext.newInstance(clazz);
			Unmarshaller unmarshaller = context.createUnmarshaller();
			return (T) unmarshaller.unmarshal(new StringReader(xml));
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("XML转对象失败");
		}
	}

	public static void main(String[] args) throws Exception {
		// 从文件读取
		
		/*JAXBContext context = JAXBContext.newInstance(OTAAirScheduleRQ.class);
		Unmarshaller unmarshaller = context.createUnmarshaller();
		OTAAirScheduleRQ dto = (OTAAirScheduleRQ) unmarshaller.unmarshal(new File(
				"d:/ibe/OTA_AirSchedule.xml"));*/

		//System.out.println(JSON.toJSONString(dto));
		
		//System.out.println(object2Xml(dto));

	}

}
