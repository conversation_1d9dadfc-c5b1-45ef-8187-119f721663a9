package com.llq.vj.util;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * HTML解析工具类
 * 用于解析Vietjet航班预订页面
 * 
 * <AUTHOR>
 * @version 2025/10/27
 */
public class HtmlParserUtil {

    /**
     * 从HTML文件中解析出指定的radio button value值
     * 
     * @param htmlFilePath HTML文件路径
     * @param targetValue 要查找的目标值，如 "4,A1_ECO,O"
     * @return 找到的值，如果没找到返回null
     */
    public static String parseRadioButtonValue(String htmlFilePath, String targetValue) {
        try {
            File htmlFile = new File(htmlFilePath);
            Document doc = Jsoup.parse(htmlFile, "UTF-8");
            
            // 查找所有radio button
            Elements radioButtons = doc.select("input[type=radio]");
            
            for (Element radio : radioButtons) {
                String value = radio.attr("value");
                if (targetValue.equals(value)) {
                    return value;
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        
        return null;
    }

    /**
     * 从HTML文件中解析出所有radio button的value值
     * 
     * @param htmlFilePath HTML文件路径
     * @return 所有radio button的value值列表
     */
    public static List<String> parseAllRadioButtonValues(String htmlFilePath) {
        List<String> values = new ArrayList<>();
        
        try {
            File htmlFile = new File(htmlFilePath);
            Document doc = Jsoup.parse(htmlFile, "UTF-8");
            
            // 查找所有radio button
            Elements radioButtons = doc.select("input[type=radio]");
            
            for (Element radio : radioButtons) {
                String value = radio.attr("value");
                if (value != null && !value.isEmpty()) {
                    values.add(value);
                }
            }
            
        } catch (IOException e) {
            e.printStackTrace();
        }
        
        return values;
    }

    /**
     * 从HTML文件中解析出符合特定模式的radio button value值
     * 模式：数字,字母数字_字母,字母 (如: 4,A1_ECO,O)
     * 
     * @param htmlFilePath HTML文件路径
     * @return 符合模式的value值列表
     */
    public static List<String> parseFlightOptionValues(String htmlFilePath) {
        List<String> values = new ArrayList<>();
        
        try {
            File htmlFile = new File(htmlFilePath);
            Document doc = Jsoup.parse(htmlFile, "UTF-8");
            
            // 查找所有radio button
            Elements radioButtons = doc.select("input[type=radio]");
            
            // 定义匹配模式：数字,字母数字_字母,字母
            Pattern pattern = Pattern.compile("^\\d+,[A-Z0-9]+_[A-Z]+,[A-Z]$");
            
            for (Element radio : radioButtons) {
                String value = radio.attr("value");
                if (value != null && pattern.matcher(value).matches()) {
                    values.add(value);
                }
            }
            
        } catch (IOException e) {
            e.printStackTrace();
        }
        
        return values;
    }

    /**
     * 从HTML文件中解析出特定name属性的radio button value值
     * 
     * @param htmlFilePath HTML文件路径
     * @param radioName radio button的name属性值
     * @return 该name下所有radio button的value值列表
     */
    public static List<String> parseRadioButtonValuesByName(String htmlFilePath, String radioName) {
        List<String> values = new ArrayList<>();
        
        try {
            File htmlFile = new File(htmlFilePath);
            Document doc = Jsoup.parse(htmlFile, "UTF-8");
            
            // 查找指定name的radio button
            Elements radioButtons = doc.select("input[type=radio][name=" + radioName + "]");
            
            for (Element radio : radioButtons) {
                String value = radio.attr("value");
                if (value != null && !value.isEmpty()) {
                    values.add(value);
                }
            }
            
        } catch (IOException e) {
            e.printStackTrace();
        }
        
        return values;
    }

    /**
     * 解析航班选项详细信息
     * 
     * @param htmlFilePath HTML文件路径
     * @param targetValue 目标值，如 "4,A1_ECO,O"
     * @return 航班选项详细信息
     */
    public static FlightOptionInfo parseFlightOptionInfo(String htmlFilePath, String targetValue) {
        try {
            File htmlFile = new File(htmlFilePath);
            Document doc = Jsoup.parse(htmlFile, "UTF-8");
            
            // 查找目标radio button
            Elements radioButtons = doc.select("input[type=radio][value=" + targetValue + "]");
            
            if (!radioButtons.isEmpty()) {
                Element radio = radioButtons.first();
                Element parentTd = radio.parent();
                
                FlightOptionInfo info = new FlightOptionInfo();
                info.setValue(targetValue);
                info.setName(radio.attr("name"));
                info.setOnClick(radio.attr("onclick"));
                
                // 解析价格信息
                Elements hiddenInputs = parentTd.select("input[type=hidden]");
                for (Element hidden : hiddenInputs) {
                    String id = hidden.attr("id");
                    String value = hidden.attr("value");
                    
                    switch (id) {
                        case "fare":
                            info.setFare(value);
                            break;
                        case "fare_taxes":
                            info.setFareTaxes(value);
                            break;
                        case "total_fare":
                            info.setTotalFare(value);
                            break;
                        case "charges":
                            info.setCharges(value);
                            break;
                        case "total_complete_charges":
                            info.setTotalCompleteCharges(value);
                            break;
                    }
                }
                
                return info;
            }
            
        } catch (IOException e) {
            e.printStackTrace();
        }
        
        return null;
    }

    /**
     * 航班选项信息类
     */
    public static class FlightOptionInfo {
        private String value;
        private String name;
        private String onClick;
        private String fare;
        private String fareTaxes;
        private String totalFare;
        private String charges;
        private String totalCompleteCharges;

        // Getters and Setters
        public String getValue() { return value; }
        public void setValue(String value) { this.value = value; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getOnClick() { return onClick; }
        public void setOnClick(String onClick) { this.onClick = onClick; }
        
        public String getFare() { return fare; }
        public void setFare(String fare) { this.fare = fare; }
        
        public String getFareTaxes() { return fareTaxes; }
        public void setFareTaxes(String fareTaxes) { this.fareTaxes = fareTaxes; }
        
        public String getTotalFare() { return totalFare; }
        public void setTotalFare(String totalFare) { this.totalFare = totalFare; }
        
        public String getCharges() { return charges; }
        public void setCharges(String charges) { this.charges = charges; }
        
        public String getTotalCompleteCharges() { return totalCompleteCharges; }
        public void setTotalCompleteCharges(String totalCompleteCharges) { this.totalCompleteCharges = totalCompleteCharges; }

        @Override
        public String toString() {
            return "FlightOptionInfo{" +
                    "value='" + value + '\'' +
                    ", name='" + name + '\'' +
                    ", fare='" + fare + '\'' +
                    ", totalFare='" + totalFare + '\'' +
                    ", charges='" + charges + '\'' +
                    ", totalCompleteCharges='" + totalCompleteCharges + '\'' +
                    '}';
        }
    }

    /**
     * 根据航班条件智能选择航班参数
     * @param htmlFilePath HTML文件路径
     * @param flightNumber 航班号，如 "VJ931"
     * @param fareCodePrefix 票价代码前缀，如 "A1" (可选)
     * @param cabinClass 舱位类型，如 "ECO", "DLX", "SkyBoss" (可选)
     * @param maxPrice 最大价格 (可选)
     * @return 选择的航班参数值，如 "4,A1_ECO,O"
     */
    public static String selectFlightParameter(String htmlFilePath, String flightNumber,
                                             String fareCodePrefix, String cabinClass, Double maxPrice) {
        try {
            File htmlFile = new File(htmlFilePath);
            Document doc = Jsoup.parse(htmlFile, "UTF-8");

            // 获取所有符合模式的radio按钮
            Elements radioButtons = doc.select("input[type=radio]");
            Pattern pattern = Pattern.compile("^\\d+,[A-Z0-9]+_[A-Z]+,[A-Z]$");

            List<FlightCandidate> candidates = new ArrayList<>();

            for (Element radio : radioButtons) {
                String value = radio.attr("value");
                if (value != null && pattern.matcher(value).matches()) {
                    FlightCandidate candidate = parseFlightCandidate(radio, value);
                    if (candidate != null) {
                        candidates.add(candidate);
                    }
                }
            }

            // 根据条件筛选
            List<FlightCandidate> filtered = candidates.stream()
                    .filter(c -> matchesFlightCriteria(c, flightNumber, fareCodePrefix, cabinClass, maxPrice))
                    .collect(Collectors.toList());

            if (filtered.isEmpty()) {
                return null;
            }

            // 选择最佳选项（价格最低的）
            FlightCandidate selected = filtered.stream()
                    .min(Comparator.comparing(FlightCandidate::getPrice))
                    .orElse(null);

            return selected != null ? selected.getValue() : null;

        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 解析航班候选项
     */
    private static FlightCandidate parseFlightCandidate(Element radio, String value) {
        Element parentTd = radio.closest("td");
        if (parentTd == null) return null;

        FlightCandidate candidate = new FlightCandidate();
        candidate.setValue(value);
        candidate.setName(radio.attr("name"));

        // 解析value中的信息
        String[] parts = value.split(",");
        if (parts.length >= 3) {
            candidate.setFlightIndex(parts[0]);
            candidate.setFareCode(parts[1]);
            candidate.setCabinClass(parts[2]);
        }

        // 获取价格信息
        Elements hiddenInputs = parentTd.select("input[type=hidden][id=fare]");
        if (!hiddenInputs.isEmpty()) {
            String fareStr = hiddenInputs.first().attr("value");
            try {
                double price = Double.parseDouble(fareStr.replaceAll("[^\\d.]", ""));
                candidate.setPrice(price);
            } catch (NumberFormatException e) {
                candidate.setPrice(Double.MAX_VALUE);
            }
        }

        // 获取舱位类型
        String familyId = parentTd.attr("data-familyid");
        candidate.setFamilyId(familyId);

        return candidate;
    }

    /**
     * 检查航班候选项是否符合条件
     */
    private static boolean matchesFlightCriteria(FlightCandidate candidate, String flightNumber,
                                               String fareCodePrefix, String cabinClass, Double maxPrice) {
        // 检查票价代码前缀
        if (fareCodePrefix != null && !candidate.getFareCode().startsWith(fareCodePrefix)) {
            return false;
        }

        // 检查舱位类型
        if (cabinClass != null && !candidate.getFareCode().contains(cabinClass)) {
            return false;
        }

        // 检查最大价格
        if (maxPrice != null && candidate.getPrice() > maxPrice) {
            return false;
        }

        return true;
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        String htmlFilePath = "src/main/java/com/llq/vj/payment/tast.html";

        // 1. 查找特定值
        String targetValue = "4,A1_ECO,O";
        String foundValue = parseRadioButtonValue(htmlFilePath, targetValue);
        System.out.println("找到的值: " + foundValue);

        // 2. 获取所有航班选项值
        List<String> flightOptions = parseFlightOptionValues(htmlFilePath);
        System.out.println("所有航班选项值:");
        flightOptions.forEach(System.out::println);

        // 3. 获取详细信息
        FlightOptionInfo info = parseFlightOptionInfo(htmlFilePath, targetValue);
        if (info != null) {
            System.out.println("详细信息: " + info);
        }

        // 4. 智能选择航班参数
        String selectedParam = selectFlightParameter(htmlFilePath, "VJ931", "A1", "ECO", 30.0);
        System.out.println("智能选择的参数: " + selectedParam);

        // 5. 获取特定name的所有值
        List<String> retValues = parseRadioButtonValuesByName(htmlFilePath, "gridTravelOptRet");
        System.out.println("返程航班选项:");
        retValues.forEach(System.out::println);
    }

    /**
     * 航班候选项类
     */
    public static class FlightCandidate {
        private String value;
        private String name;
        private String flightIndex;
        private String fareCode;
        private String cabinClass;
        private String familyId;
        private double price;

        // Getters and Setters
        public String getValue() { return value; }
        public void setValue(String value) { this.value = value; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getFlightIndex() { return flightIndex; }
        public void setFlightIndex(String flightIndex) { this.flightIndex = flightIndex; }

        public String getFareCode() { return fareCode; }
        public void setFareCode(String fareCode) { this.fareCode = fareCode; }

        public String getCabinClass() { return cabinClass; }
        public void setCabinClass(String cabinClass) { this.cabinClass = cabinClass; }

        public String getFamilyId() { return familyId; }
        public void setFamilyId(String familyId) { this.familyId = familyId; }

        public double getPrice() { return price; }
        public void setPrice(double price) { this.price = price; }

        @Override
        public String toString() {
            return "FlightCandidate{" +
                    "value='" + value + '\'' +
                    ", fareCode='" + fareCode + '\'' +
                    ", price=" + price +
                    ", familyId='" + familyId + '\'' +
                    '}';
        }
    }
}
