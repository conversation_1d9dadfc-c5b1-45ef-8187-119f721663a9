package com.llq.vj.http;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.llq.vj.dto.VjResultDataDto;
import com.llq.vj.dto.VjResultDto;
import com.llq.vj.dto.VjResultPriceDataDto;
import com.llq.vj.dto.VjResultPriceDataSegmentDto;
import com.llq.vj.service.impl.Lcc_HuilvServiceImpl;
import com.llq.vj.service.impl.Lcc_VjGetDataServiceImpl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class VJDataInfo {

    public VjResultDataDto dataInfo(String param){


        VjResultDataDto vjResultDataDto = new VjResultDataDto();

        JSONObject jsonObject = JSONObject.parseObject(param);
        JSONObject travelOption = jsonObject.getJSONObject("travelOption");
        for (String key : travelOption.keySet()) {


            JSONArray jsonArray = travelOption.getJSONArray(key);
            if (jsonArray.isEmpty()){
                continue;
            }
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject json = jsonArray.getJSONObject(i);
                String departureDate = json.getString("departureDate");

                String dep_airPort = key.split("-")[0];
                String arr_airPort = key.split("-")[1];


                vjResultDataDto.setDepCity(dep_airPort);
                vjResultDataDto.setArrCity(arr_airPort);
                vjResultDataDto.setDateStr_go(departureDate);

                List<VjResultPriceDataSegmentDto> vjResultPriceDataSegmentDtos = new ArrayList<VjResultPriceDataSegmentDto>();


                JSONArray flights = json.getJSONArray("flights");

                String fareType = "";
                Integer availability = 0;
                JSONArray fareOptions = json.getJSONArray("fareOptions");

                double fare = 0;
                BigDecimal tax = new BigDecimal(0);
                String currency = "";
                for (int j = 0; j < fareOptions.size(); j++) {
                    JSONObject fareOption = fareOptions.getJSONObject(j);

                    availability = fareOption.getInteger("availability");
                    if (availability < 1 ){
                        continue;
                    }

                    fareType = fareOption.getJSONObject("fareType").getString("description");
                    if (!fareType.equals("Eco") && !fareType.equals("Deluxe")){
                        continue;
                    }

                    JSONArray fareCharges = fareOption.getJSONArray("fareCharges");
                    for (int k = 0; k < fareCharges.size(); k++) {
                        JSONObject fareCharge = fareCharges.getJSONObject(k);
                        String description = fareCharge.getJSONObject("chargeType").getString("description");
                        if (description.equals("Fare")){
                            fare = fareCharge.getJSONArray("currencyAmounts").getJSONObject(0).getDouble("baseAmount");
                            continue;
                        }

                        if (description.equals("Seat Assignment")){
                            continue;
                        }

                        BigDecimal tax1 = new BigDecimal(fareCharge.getJSONArray("currencyAmounts").getJSONObject(0).getDouble("baseAmount"));
                        tax = tax.add(tax1);
                        currency = fareCharge.getJSONArray("currencyAmounts").getJSONObject(0).getJSONObject("currency").getString("code");
                        if (availability < 1){
                            continue;
                        }


                        for (int l = 0; l < flights.size(); l++) {
                            JSONObject flight = flights.getJSONObject(l);
                            String airlineCode = flight.getJSONObject("airlineCode").getString("code");
                            String flightNumber = flight.getString("flightNumber");

                            JSONObject departure = flight.getJSONObject("departure");
                            JSONObject arrival = flight.getJSONObject("departure");

                            String aircraftModel = flight.getJSONObject("aircraftModel").getString("identifier");

                            String dep_time = departure.getString("localScheduledTime");
                            String arr_time = arrival.getString("localScheduledTime");

                            String dep = departure.getJSONObject("airport").getString("code");
                            String arr = arrival.getJSONObject("airport").getString("code");

                            VjResultPriceDataSegmentDto segment = new VjResultPriceDataSegmentDto();
                            segment.setFlightNumber(airlineCode+flightNumber);
                            segment.setDepAirport(dep);
                            segment.setArrAirport(arr);
                            segment.setDepTime(dep_time);
                            segment.setArrTime(arr_time);
                            segment.setSeatClass("X");
                            segment.setFareBasis(fareType);

                            if (fareType.equals("Deluxe")){
                                segment.setBaggageQuantity(1);
                                segment.setBaggageWeight(1);
                            } else {
                                segment.setBaggageQuantity(0);
                                segment.setBaggageWeight(0);
                            }

                            vjResultPriceDataSegmentDtos.add(segment);
                        }

                        VjResultPriceDataDto vjResultPriceDataDto = new VjResultPriceDataDto();
                        vjResultPriceDataDto.setOriginalCurrency(currency);

                        Lcc_HuilvServiceImpl lccHuilvService = new Lcc_HuilvServiceImpl();
                        BigDecimal exchangeRate = lccHuilvService.getExchangeRate(currency, "CNY");

//                        BigDecimal exchangeRate = new Lcc_VjGetDataServiceImpl().getExchangeRate(currency);
                        vjResultPriceDataDto.setExchangeRate(exchangeRate);
                        vjResultPriceDataDto.setSeatCount(availability);
                        vjResultPriceDataDto.setBasePrice(new BigDecimal(fare));
                        vjResultPriceDataDto.setTaxFeeAount(tax);
                        vjResultPriceDataDto.setPriceDataSegmentDtoList_go(vjResultPriceDataSegmentDtos);

                        if (fareType.equals("Deluxe")){
                            vjResultPriceDataDto.setBaggageQuantity(1);
                            vjResultPriceDataDto.setBaggageWeight(1);
                        }else {
                            vjResultPriceDataDto.setBaggageQuantity(0);
                            vjResultPriceDataDto.setBaggageWeight(0);
                        }

                        if (fareType.equals("Eco")){
                            vjResultDataDto.setBasicPriceDto(vjResultPriceDataDto);
                        }else {
                            vjResultDataDto.setStandardPriceDto(vjResultPriceDataDto);
                        }

                    }

                }

            }

        }
        return vjResultDataDto;
    }
}
