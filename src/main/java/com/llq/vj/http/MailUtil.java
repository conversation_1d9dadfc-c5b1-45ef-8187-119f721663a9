package com.llq.vj.http;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.IdUtil;
import com.llq.vj.config.MailConfig;
import com.llq.vj.dto.MailDataDto;
import com.llq.vj.dto.MailDto;
import com.llq.vj.dto.MailFileDataDto;
import com.llq.vj.enums.AirLineType;
import com.llq.vj.enums.MailGroupConfig;
import com.llq.vj.enums.MailHostType;
import com.sun.mail.imap.IMAPFolder;
import com.sun.mail.imap.IMAPStore;
import lombok.extern.slf4j.Slf4j;

import javax.mail.*;
import javax.mail.search.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025/07/12
 **/
@Slf4j
public class MailUtil {

    // 客户端身份信息
    private static final Map<String, String> IAM = new HashMap<String, String>() {{
        put("name", "saas667");
        put("version", "1.0");
        put("vendor", "llq");
        put("support-email", "<EMAIL>");
    }};

    /**
     * 获取未读的邮件
     *
     * @return
     */
    public static MailDto receiveUnRead(AirLineType airLine) {
        MailGroupConfig mailGroupConfig = MailGroupConfig.getConfigByAir(airLine);
        MailHostType hostType = mailGroupConfig.getHostType();
        IMAPStore store = null;
        try {
            Session session = Session.getInstance(hostType.getProp());
            store = (IMAPStore) session.getStore(hostType.getStore());
            store.connect(hostType.getHost(),
                    mailGroupConfig.getUsername(), mailGroupConfig.getAuthCode());
            // 必须添加的客户端身份信息
            store.id(IAM);
            IMAPFolder inbox = (IMAPFolder) store.getFolder("INBOX");
            inbox.open(Folder.READ_ONLY);
            // 获取邮件
            Message[] messages = inbox.search(new FlagTerm(
                    new Flags(Flags.Flag.SEEN), false));
            List<MailDataDto> dtoList = parseImapMessage(inbox, messages);
            //设置已读
            Arrays.stream(messages).forEach(message -> {
                try {
                    // 已读
                    message.setFlag(Flags.Flag.SEEN, true);
                    message.saveChanges();
                } catch (MessagingException e) {
                    e.printStackTrace();
                }
            });
            inbox.close(false);
            return MailDto.ok(dtoList);
        } catch (Exception e) {
            return MailDto.fail(e.getMessage());
        } finally {
            close(store);
        }
    }

    /**
     * 获取指定时间段的邮件
     *
     * @param airLine
     * @param startDate
     * @param endDate
     * @return
     */
    public static MailDto receiveBetween(AirLineType airLine, Date startDate, Date endDate) {
        List<MailDataDto> dtoList = null;
        try {
            // 获取配置
            MailGroupConfig mailGroupConfig = MailGroupConfig.getConfigByAir(airLine);
            MailHostType hostType = mailGroupConfig.getHostType();
            if (hostType.getStore().equals("imaps")) {
                // 日期段
                SearchTerm endTerm = new ReceivedDateTerm(ComparisonTerm.LE, endDate);
                SearchTerm startTerm = new ReceivedDateTerm(ComparisonTerm.GE, startDate);
                SearchTerm dateRangeTerm = new AndTerm(startTerm, endTerm);
                dtoList = imap(mailGroupConfig, dateRangeTerm);
            } else {
            }
            return MailDto.ok(dtoList);
        } catch (Exception e) {
            e.printStackTrace();
            return MailDto.fail(e.getMessage());
        }
    }

    /**
     * 获取IMAP
     *
     * @param mailGroupConfig
     * @param searchTerm
     * @return
     */
    private static List<MailDataDto> imap(MailGroupConfig mailGroupConfig,
                                          SearchTerm searchTerm) {
        IMAPStore store = null;
        try {
            MailHostType hostType = mailGroupConfig.getHostType();
            Session session = Session.getInstance(hostType.getProp());
            store = (IMAPStore) session.getStore(hostType.getStore());
            store.connect(hostType.getHost(),
                    mailGroupConfig.getUsername(),
                    mailGroupConfig.getAuthCode());
            // 必须添加的客户端身份信息
            store.id(IAM);
            IMAPFolder inbox = (IMAPFolder) store.getFolder("INBOX");
            inbox.open(Folder.READ_ONLY);
            // 获取邮件
            Message[] messages = inbox.search(searchTerm);
            List<MailDataDto> dtoList = parseImapMessage(inbox, messages);
            inbox.close(false);
            return dtoList;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("MailUtil-getImap-ex: " + e.getMessage());
        } finally {
            close(store);
        }
    }


    /**
     * 解析邮件内容
     *
     * @param inbox
     * @param messages
     * @return
     * @throws MessagingException
     * @throws IOException
     */
    private static List<MailDataDto> parseImapMessage(UIDFolder inbox, Message[] messages) {
        List<MailDataDto> dtoList = new ArrayList<>();
        // 存储邮件
        for (Message message : messages) {
            try {
                MailDataDto dataDto = new MailDataDto();
                //发件人
                String from = Arrays.stream(message.getFrom())
                        .map(Address::getType)
                        .collect(Collectors.joining());
                dataDto.setFrom(from);
                //主题
                dataDto.setSubject(message.getSubject());
                //收件日期
                dataDto.setReceiveDate(message.getReceivedDate());
                //发件日期
                dataDto.setSendDate(message.getSentDate());
                //邮件ID
                long msgId = getUid(inbox, message);
                dataDto.setMsgId(msgId + "");
                // 内容
                parseContent(message, dataDto);
                //邮件ID
                dtoList.add(dataDto);
            } catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException("邮件内容解析异常:" + e.getMessage());
            }
        }
        return dtoList;
    }


    /**
     * 保存邮件体
     *
     * @param text
     * @return
     */
    private static String saveBody(String text) throws Exception {
        //获取相对路径
        String date = DateUtil.formatDate(DateUtil.date());
        String fileUrl = date + "/s7body" + IdUtil.simpleUUID()
                + ".txt";
        String path = MailConfig.path + "/" + fileUrl;
        //处理目录和文件
        File dir = new File(path).getParentFile();
        if (!dir.exists()) {
            dir.mkdirs();
        }
        log.debug("MailUtil-saveBody>>>>>>{}", path);
        new File(path).createNewFile();
        //保存相对路径
        FileUtil.writeUtf8String(text, new File(path));
        return fileUrl;
    }


    /**
     * 获取邮件UID
     *
     * @param folder
     * @param message
     * @return
     */
    public static long getUid(UIDFolder folder, Message message) {
        try {
            return folder.getUID(message);
        } catch (MessagingException e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 关闭存储区
     *
     * @param store
     */
    private static void close(Store store) {
        if (null != store) {
            try {
                store.close();
            } catch (MessagingException e) {
            }
        }
    }


    /**
     * 解析邮件内容
     *
     * @param message
     * @param dataDto
     * @return
     */
    private static void parseContent(Message message, MailDataDto dataDto) throws Exception {
        String msgId = dataDto.getMsgId();
        System.out.println(message.getContentType());
        if (message.isMimeType("text/*")) {
            String text = (String) message.getContent();
            String url = saveBody(text);
            dataDto.setContent(url);
        } else if (message.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) message.getContent();
            for (int i = 0; i < multipart.getCount(); i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                if (bodyPart.isMimeType("text/*")) {
                    String text = (String) bodyPart.getContent();
                    String url = saveBody(text);
                    dataDto.setContent(url);
                } else if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
                    List<MailFileDataDto> attachments = dataDto.getAttachments();
                    String seq = msgId + i;
                    try (InputStream is = bodyPart.getInputStream()) {
                        long size = bodyPart.getSize();
                        MailFileDataDto fileDataDto = saveAttachment(bodyPart.getFileName(), is, size, seq);
                        attachments.add(fileDataDto);
                    }
                }else{

                }
            }
        }
    }

    /**
     * 保存附件
     *
     * @param fileName
     * @param is
     * @param size
     * @param seq
     * @return
     */
    private static MailFileDataDto saveAttachment(String fileName, InputStream is,
                                                  long size, String seq) throws IOException {
        MailFileDataDto fileDataDto = new MailFileDataDto();
        fileDataDto.setFileName(fileName);
        //获取相对路径
        String date = DateUtil.formatDate(DateUtil.date());
        String fileUrl = date + "/s7file" + seq + IdUtil.simpleUUID()
                + "." + FileNameUtil.extName(fileName);
        String path = MailConfig.path + "/" + fileUrl;
        log.debug("MailUtil-saveAttachment>>>>>>{}", path);
        //处理目录和文件
        File dir = new File(path).getParentFile();
        if (!dir.exists()) {
            dir.mkdirs();
        }
        new File(path).createNewFile();
        //保存相对路径
        fileDataDto.setFileUrl(fileUrl);
        fileDataDto.setSize(size);
        try (FileOutputStream fos = new FileOutputStream(path)) {
            byte[] buf = new byte[4096];
            int bytesRead;
            while ((bytesRead = is.read(buf)) != -1) {
                fos.write(buf, 0, bytesRead);
            }
        }
        return fileDataDto;
    }


    public static void main(String[] args) throws ParseException {

        Date startDate = new SimpleDateFormat("yyyy-MM-dd").parse("2025-07-16");
        Date endDate = new SimpleDateFormat("yyyy-MM-dd").parse("2025-07-16");

        MailConfig.userName = "<EMAIL>";
        MailConfig.authCode = "AFgvvDWVbyaJz5uc";
        MailConfig.path = "d:/lcc/mail";
        MailDto dto = receiveBetween(AirLineType.VJ, startDate, endDate);
        System.out.println(dto.isSuccess());
        dto.getData().stream().forEach(mailDataDto -> System.out.println(">>>>>>>" + mailDataDto));
        System.out.println();
    }


}
