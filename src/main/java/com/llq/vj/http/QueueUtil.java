package com.llq.vj.http;

import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @version 2025/07/23
 **/
@Slf4j
public class QueueUtil {


    private static final LinkedBlockingQueue<String> queue = new LinkedBlockingQueue<String>();

    private static final HttpClient httpClient = HttpClients.createDefault();
    private static final String URL = "http://luluqi.user.xiecaiyun.com/api/proxies?action=getText&key=NPEEA975B7&count=100&word=&rand=false&norepeat=false&detail=true&ltime=&idshow=false";

    private static long DATA_EXPIRY_DATE = 0;


    /**
     * 如果为空加载
     * <p>
     * 防止重复加载
     */
    private static synchronized void getIp() {
        if (empty()) {
            int loop = 10;
            do {
                if (ApiRateLimiter.tryAccess()) {
                    getRateIp();
                    break;
                } else {
                    try {
                        Thread.sleep(100);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                loop--;
            } while (loop > 0);
        }
    }

    /**
     * 获取代理IP
     */
    private static void getRateIp() {
        // 5分钟过期
        DATA_EXPIRY_DATE = System.currentTimeMillis() + 5 * 60 * 1000;
        HttpGet httpGet = new HttpGet(URL);
        try {
            HttpResponse response = httpClient.execute(httpGet);
            String responseBody = EntityUtils.toString(response.getEntity());
            List<String> ipList = Splitter.on("\r\n").trimResults()
                    .splitToList(responseBody);
            queue.addAll(ipList);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void addProxy(String proxy) {
        queue.offer(proxy);
    }


    /**
     * 执行前清空数据
     */
    private static void clear() {
        queue.clear();
    }


    /**
     * 获取第一个元素
     *
     * @return
     */
    public static String poll() {
        // 如果超过5MIN
        if (System.currentTimeMillis() > DATA_EXPIRY_DATE) {
            clear();
            getIp();
        }

        //此时获取如果为空，再查询一次
        String ip = queue.poll();
        if (Strings.isNullOrEmpty(ip)) {
            getIp();
            ip = queue.poll();
        }
        return ip;
    }


    /**
     * 判断是否为空
     *
     * @return
     */
    public static boolean empty() {
        return queue.isEmpty();
    }


    /**
     * 推入一个元素
     *
     * @param vo
     */
    public static void offer(String vo) {
        queue.offer(vo);
    }


}
