package com.llq.vj.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.llq.vj.dto.VjResultDataDto;
import com.llq.vj.dto.VjResultPriceDataDto;
import com.llq.vj.vo.VjApiResultVo;
import com.llq.vj.vo.VjDataVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/07/09
 **/
@Component
@Slf4j
public class VjPostUtil {


    @Resource
    private RestTemplate restTemplate;



    /**
     * 接口请求
     *
     * @param json
     * @return
     */
    public VjApiResultVo post(String url, String json) {
        //return this.publicPost(url, json);
        int loop = 10;
        while (loop > 0){
            if ( ApiRateLimiter.tryAccess()){
                return this.privatePost(json);
            }else {
                try{Thread.sleep(100); }catch (Exception e){
                    e.printStackTrace();
                }
            }
            loop--;
        }
        return VjApiResultVo.fail("尝试失败");
    }

    /**
     * 接口请求
     *
     * @param json
     * @return
     */
    public VjApiResultVo publicPost(String url, String json) {
        //log.info("S7PostUtil-post-url:{},param:{}" ,url,json);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(json, headers);
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            if (HttpStatus.OK.equals(response.getStatusCode())) {
                String retJson = response.getBody();
                JSONObject retJsonObject = JSON.parseObject(retJson);
                int code = retJsonObject.getIntValue("code");
                if (code == 200) {
                    JSONObject retDataJsonObject = retJsonObject.getJSONObject("data");
                    //S7DataVo s7DataVo = retDataJsonObject.toJavaObject(S7DataVo.class);
                    return VjApiResultVo.ok(null);
                }
                String msg = retJsonObject.getString("data");
                return VjApiResultVo.fail(msg);
            }
            return VjApiResultVo.fail(response.toString());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("S7PostUtil-post-ex:" + e.getMessage());
            return VjApiResultVo.fail(e.getMessage());
        }
    }


    /**
     *
     * 自研调用
     * @param json
     * @return
     */
    public VjApiResultVo privatePost(String json) {
        //log.info("S7PostUtil-post-url:{},param:{}" ,url,json);
        try {
           String ret =  new VJTask().wrapperRequest(json);
           if (Strings.isNullOrEmpty(ret) || ret.contains("ERROR_")){
               return VjApiResultVo.fail(ret);
           }else {
               JSONObject retJsonObject = JSON.parseObject(ret);
               int code = retJsonObject.getIntValue("code");
               if (code == 200) {
                   JSONObject retDataJsonObject = retJsonObject.getJSONObject("data");
                   /**
                    *
                    * "message": "founded",
                    * "status": 1
                    */
                   if (retDataJsonObject.getIntValue("status") == 1){
                       if(true){
                           VJDataInfo vjDataInfo = new VJDataInfo();
                           List<VjResultDataDto> s7DataVo = vjDataInfo.dataInfo(ret,json);
//                           VjResultDataDto s7DataVo = retDataJsonObject.getJSONObject("data")
//                                   .toJavaObject(VjResultDataDto.class);
                           return VjApiResultVo.ok(s7DataVo);
                       }else {
                           return VjApiResultVo.fail("缺data字段");
                       }
                   } else {
                       String msg = retJsonObject.getString("data");
                       return VjApiResultVo.fail(msg);
                   }
               }
               return VjApiResultVo.fail(ret);
           }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("S7PostUtil-post-ex:" + e.getMessage());
            return VjApiResultVo.fail(e.getMessage());
        }
    }
}