package com.llq.vj.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2025/09/12
 **/
@Data
public class VjPriceDetailVo {


    /**
     * 81.9
     */
    private BigDecimal fare_tax;

    /**
     * 14.82
     */
    private BigDecimal tax_fee_tax;


    /**
     * 0
     */
    private Integer addon_tax;

    /**
     * 1170
     */
    private Integer fare_base;


    /**
     * 341.73
     */
    private BigDecimal tax_fee_base;

    /**
     * 0
     */
    private Integer addon_base;

    /**
     * 0
     */
    private Integer fare_discount;

    /**
     * 0
     */
    private Integer tax_fee_discount;

    /**
     * 0
     */
    private Integer addon_discount;

    /**
     * 1251.9
     */
    private BigDecimal fare_total;

    /**
     * 356.54999999999995
     */
    private BigDecimal tax_fee_total;

    /**
     * 0
     */
    private Integer addon_total;


    /**
     *
     */
    private String tax_fee;


}
