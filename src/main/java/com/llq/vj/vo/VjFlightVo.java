package com.llq.vj.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 2025/09/12
 **/
@Data
public class VjFlightVo {


    /**
     * VZ
     */
    private String airline_code;

    /**
     * Thai VietJet Air
     */
    private String airline_name;

    /**
     * 306
     */
    private String flight_number;

    /**
     * 321
     */
    private String aircraft_model;

    /**
     * Airbus A321
     */
    private String aircraft_model_name;

    /**
     * BangKok (Suvarnabhumi)
     */
    private String departure;

    /**
     * BKK
     */
    private String departure_code;

    /**
     * Phuket
     */
    private String arrival;

    /**
     * HKT
     */
    private String arrival_code;

    /**
     * 01h 30m
     */
    private String trip_hour;

    /**
     *
     */
    private VjDateVo departure_date;

    /**
     *
     *
     * 2025-09-10 15:20:00
     */
    private String departure_date_html;

    /**
     *
     *
     */
    private VjDateVo arrival_date;

    /**
     *
     *
     * 2025-09-10 16:50:00
     */
    private String arrival_date_html;

    /**
     *
     *
     * Wed Sep 10 15:20 PM
     */
    private String departure_time;

    /**
     *
     *
     * Wed, 10 Sep 2025
     */
    private String departure_time_html;

    /**
     *
     *
     * 15:20
     */
    private String departure_hour;

    /**
     *
     *
     * 10 Sep
     */
    private String departure_dm;

    /**
     *
     *
     * 10/09/2025
     */
    private String departure_format_html;

    /**
     *
     *
     * Wed Sep 10 16:50 PM
     */
    private String arrival_time;

    /**
     *
     *
     * 16:50
     */
    private String arrival_hour;

    /**
     *
     *
     * 10 Sep
     */
    private String arrival_dm;

    /**
     *
     *
     * 10/09/2025
     */
    private String arrival_format_html;


}
