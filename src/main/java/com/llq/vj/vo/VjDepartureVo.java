package com.llq.vj.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/09/12
 **/
@Data
public class VjDepartureVo {

    /**
     * 0
     */
    private Integer index;

    /**
     *
     * QcCbieEcFuYhONTHBja6Tƒ8ƒntFPuWwwFnsTr1y4rq3PBrXnomyOrSQvWKgGvXcOPSR8pjfpKn30Wv0uXTlJVfgHO¥mgGj5mDv36IEpCbHlKGeWeMCfU1pFzOYyk6LQtw3jPla57vS3XjXSdQƒ99rlfpVFyAC¥eqkgZrqFaqtAb6BfkdlqGhspHPN3WaenkhpOinO2Zd5Bi7w1yjl5DHsQ==
     *
     */
    private String id;


    /**
     * <PERSON><PERSON><PERSON> - <PERSON> to Phuket
     */
    private String name;

    /**
     * BKK → HKT
     */
    private String route_name;


    /**
     *
     * 1
     */
    private Integer numberOfStops;


    /**
     *
     */
    private List<VjFlightVo> flights;


    /**
     * 1,608.45฿
     */
    private String price;

    /**
     * 1251.9
     */
    private BigDecimal adult_price;

    /**
     * 1,251.90฿
     */
    private String adult_price_html;

    /**
     * 1,251.90฿
     */
    private String total_adult_price;

    /**
     * 0.00฿
     */
    private String total_child_price;

    /**
     * 0.00฿
     */
    private String total_infant_price;

    /**
     * 1
     */
    private Integer adult_count;

    /**
     * 0
     */
    private Integer child_count;

    /**
     * 0
     */
    private Integer infant_count;

    /**
     * false
     */
    private Boolean priceIncludesTax;

    /**
     * false
     */
    private Boolean discount;


    /**
     * 2025-09-10
     */
    private String departureDate;

    /**
     * 1,170.00฿
     */
    private String base_price;

    /**
     * true
     */
    private Boolean can_book;

    /**
     * true
     */
    private Boolean from_api;

    /**
     * 1h 30m
     */
    private String enDelayTime;

    /**
     * 90
     */
    private Integer duration;

    /**
     * 01h 30m
     */
    private String enRouteHours;

    /**
     *
     */
    private String enRouteDays;


    /**
     *
     *
     */
    private VjFareTypeVo fare_types;

    /**
     *
     * VZ
     */
    private String band;

    /**
     *
     * AMELIA
     */
    private String channel_provider;


    /**
     *
     * BKK-HKT
     */
    private String city_pair;


    /**
     *
     * direct
     */
    private String transit;

    /**
     * Direct Flight
     *
     */
    private String transitText;

    /**
     * false
     *
     */
    private Boolean isMultipleStop;

    /**
     * VZ
     *
     */
    private String airline;

    /**
     * afternoon
     *
     */
    private String timeOfDay;

    /**
     * 2025-09-10 15:20
     *
     */
    private String earliestDeparture;


    /**
     * 2025-09-10 16:50
     *
     */
    private String latestArrivalLocal;



}
