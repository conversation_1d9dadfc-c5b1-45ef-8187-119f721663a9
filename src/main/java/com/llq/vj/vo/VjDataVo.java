package com.llq.vj.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2025/09/11
 **/
@Data
public class VjDataVo {


    /**
     *
     *
     *
     * JSON数据
     */
    private String departure_list;

    /**
     *
     * JSON数据
     * 转对象
     */
    private List<VjDepartureVo> departureVoList;

    /**
     * cityPairSearch
     */
    private VjCityPairSearchVo cityPairSearch;


    /**
     * fare_class_list
     */
    private List<VjFareClassVo> fare_class_list;


    /**
     * paramsSearch
     */
    private VjParamsSearchVo paramsSearch;

    /**
     * promoCode : ""
     */
    private String promoCode;

    /**
     * promoCodeDiscount : ""
     */
    private String promoCodeDiscount;

    /**
     * memberPromoCode : ""
     */
    private String memberPromoCode;

    /**
     * api_uuid : "xPqnlWXkOiPtD478VM9iCBdc9P95oBn3L01BmAHU"
     */
    private String api_uuid;




}
